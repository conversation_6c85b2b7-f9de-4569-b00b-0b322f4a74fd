import { useUser } from '@/contexts/UserContext';
import { router } from 'expo-router';
import { useEffect } from 'react';

export default function IndexScreen() {
  const { user, isLoading } = useUser();

  useEffect(() => {
    if (!isLoading) {
      if (user) {
        // User is logged in, redirect to appropriate dashboard
        const roleRoutes = {
          assistant: '/(tabs)/salesAssistant/dashboard',
          representative: '/(tabs)/representative/dashboard',
          recruiter: '/(tabs)/recruiter/dashboard',
          executive: '/(tabs)/executive/dashboard',
          admin: '/(tabs)/admin/dashboard',
        };

        const route = roleRoutes[user.role] || '/(tabs)/salesAssistant/dashboard';
        router.replace(route);
      } else {
        // No user, redirect to login
        router.replace('/login');
      }
    }
  }, [user, isLoading]);

  return null;
}
