import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Dimensions,
} from 'react-native';
import { IconSymbol } from '@/components/ui/IconSymbol';

const { width } = Dimensions.get('window');

// Mock performance data
const performanceData = {
  weekly: {
    totalSales: 12500,
    averageOrder: 250,
    conversionRate: 75,
    profit: 3200,
    growth: 8.5,
  },
  monthly: {
    totalSales: 52500,
    averageOrder: 275,
    conversionRate: 72,
    profit: 13800,
    growth: 12.3,
  },
  salesTrend: [
    { period: 'Mon', value: 1200 },
    { period: 'Tue', value: 1800 },
    { period: 'Wed', value: 2200 },
    { period: 'Thu', value: 1600 },
    { period: 'Fri', value: 2800 },
    { period: 'Sat', value: 3200 },
    { period: 'Sun', value: 1900 },
  ],
  topProducts: [
    { name: 'Gold Jewelry', sales: 15200, percentage: 45 },
    { name: 'Silver Items', sales: 8900, percentage: 26 },
    { name: 'Watches', sales: 6200, percentage: 18 },
    { name: 'Diamonds', sales: 3700, percentage: 11 },
  ],
  teamPerformance: [
    { name: '<PERSON>', sales: 8500, bonus: 425, conversion: 85 },
    { name: '<PERSON> <PERSON>', sales: 7200, bonus: 360, conversion: 78 },
    { name: 'Mike Brown', sales: 6800, bonus: 340, conversion: 72 },
  ],
};

export default function PerformanceScreen() {
  const [selectedPeriod, setSelectedPeriod] = useState('weekly');
  const currentData = performanceData[selectedPeriod];

  const MetricCard = ({ title, value, subtitle, icon, color = 'blue', growth }) => (
    <View className="bg-white p-4 rounded-xl shadow-sm border border-gray-100 flex-1 mx-1 mb-3">
      <View className="flex-row items-center justify-between mb-2">
        <IconSymbol name={icon} size={20} color={`#${color === 'blue' ? '3b82f6' : color === 'green' ? '10b981' : color === 'purple' ? '8b5cf6' : 'f59e0b'}`} />
        {growth && (
          <View className={`px-2 py-1 rounded-full ${growth > 0 ? 'bg-green-100' : 'bg-red-100'}`}>
            <Text className={`text-xs font-medium ${growth > 0 ? 'text-green-700' : 'text-red-700'}`}>
              {growth > 0 ? '+' : ''}{growth}%
            </Text>
          </View>
        )}
      </View>
      <Text className="text-gray-600 text-sm font-medium">{title}</Text>
      <Text className={`text-xl font-bold mt-1 text-${color}-600`}>{value}</Text>
      {subtitle && <Text className="text-gray-500 text-xs mt-1">{subtitle}</Text>}
    </View>
  );

  const SimpleChart = ({ data }) => {
    const maxValue = Math.max(...data.map(item => item.value));
    
    return (
      <View className="bg-white p-4 rounded-xl shadow-sm border border-gray-100">
        <Text className="text-lg font-semibold text-gray-900 mb-4">Sales Trend</Text>
        <View className="flex-row items-end justify-between h-32">
          {data.map((item, index) => {
            const height = (item.value / maxValue) * 100;
            return (
              <View key={index} className="items-center flex-1">
                <View 
                  className="bg-blue-500 rounded-t-sm w-6 mb-2"
                  style={{ height: `${height}%` }}
                />
                <Text className="text-gray-600 text-xs">{item.period}</Text>
              </View>
            );
          })}
        </View>
      </View>
    );
  };

  const ProductItem = ({ name, sales, percentage }) => (
    <View className="flex-row items-center justify-between py-3 border-b border-gray-100">
      <View className="flex-1">
        <Text className="text-gray-900 font-medium">{name}</Text>
        <View className="flex-row items-center mt-1">
          <View className="bg-gray-200 rounded-full h-2 flex-1 mr-2">
            <View 
              className="bg-blue-500 h-2 rounded-full"
              style={{ width: `${percentage}%` }}
            />
          </View>
          <Text className="text-gray-500 text-sm">{percentage}%</Text>
        </View>
      </View>
      <Text className="text-gray-900 font-semibold ml-4">€{sales.toLocaleString()}</Text>
    </View>
  );

  const TeamMemberCard = ({ name, sales, bonus, conversion }) => (
    <View className="bg-white p-4 rounded-xl shadow-sm border border-gray-100 mb-3">
      <View className="flex-row items-center justify-between mb-3">
        <View className="flex-row items-center">
          <View className="w-10 h-10 bg-blue-100 rounded-full items-center justify-center mr-3">
            <Text className="text-blue-600 font-semibold">{name.split(' ').map(n => n[0]).join('')}</Text>
          </View>
          <Text className="text-gray-900 font-medium">{name}</Text>
        </View>
        <View className={`px-2 py-1 rounded-full ${conversion >= 80 ? 'bg-green-100' : conversion >= 70 ? 'bg-yellow-100' : 'bg-red-100'}`}>
          <Text className={`text-xs font-medium ${conversion >= 80 ? 'text-green-700' : conversion >= 70 ? 'text-yellow-700' : 'text-red-700'}`}>
            {conversion}%
          </Text>
        </View>
      </View>
      <View className="flex-row justify-between">
        <View>
          <Text className="text-gray-500 text-sm">Sales</Text>
          <Text className="text-gray-900 font-semibold">€{sales.toLocaleString()}</Text>
        </View>
        <View>
          <Text className="text-gray-500 text-sm">Bonus</Text>
          <Text className="text-green-600 font-semibold">€{bonus}</Text>
        </View>
      </View>
    </View>
  );

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <StatusBar barStyle="dark-content" backgroundColor="#f9fafb" />
      
      {/* Header */}
      <View className="bg-white px-4 py-4 border-b border-gray-100">
        <View className="flex-row items-center justify-between mb-4">
          <Text className="text-2xl font-bold text-gray-900">Performance</Text>
          <TouchableOpacity className="w-10 h-10 bg-blue-500 rounded-full items-center justify-center">
            <IconSymbol name="chart.bar" size={20} color="white" />
          </TouchableOpacity>
        </View>

        {/* Period Selector */}
        <View className="flex-row space-x-2">
          <TouchableOpacity 
            className={`px-4 py-2 rounded-lg ${selectedPeriod === 'weekly' ? 'bg-blue-500' : 'bg-gray-100'}`}
            onPress={() => setSelectedPeriod('weekly')}
          >
            <Text className={`font-medium ${selectedPeriod === 'weekly' ? 'text-white' : 'text-gray-700'}`}>
              Weekly
            </Text>
          </TouchableOpacity>
          <TouchableOpacity 
            className={`px-4 py-2 rounded-lg ${selectedPeriod === 'monthly' ? 'bg-blue-500' : 'bg-gray-100'}`}
            onPress={() => setSelectedPeriod('monthly')}
          >
            <Text className={`font-medium ${selectedPeriod === 'monthly' ? 'text-white' : 'text-gray-700'}`}>
              Monthly
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
        {/* Key Metrics */}
        <View className="px-4 py-6">
          <View className="flex-row">
            <MetricCard 
              title="Total Sales" 
              value={`€${currentData.totalSales.toLocaleString()}`}
              icon="dollarsign.circle"
              color="green"
              growth={currentData.growth}
            />
            <MetricCard 
              title="Avg Order" 
              value={`€${currentData.averageOrder}`}
              icon="cart"
              color="blue"
            />
          </View>
          <View className="flex-row">
            <MetricCard 
              title="Conversion Rate" 
              value={`${currentData.conversionRate}%`}
              icon="arrow.up.right"
              color="purple"
            />
            <MetricCard 
              title="Total Profit" 
              value={`€${currentData.profit.toLocaleString()}`}
              icon="chart.line.uptrend.xyaxis"
              color="orange"
            />
          </View>
        </View>

        {/* Sales Trend Chart */}
        <View className="px-4 mb-6">
          <SimpleChart data={performanceData.salesTrend} />
        </View>

        {/* Top Products */}
        <View className="px-4 mb-6">
          <View className="bg-white p-4 rounded-xl shadow-sm border border-gray-100">
            <Text className="text-lg font-semibold text-gray-900 mb-4">Top Products</Text>
            {performanceData.topProducts.map((product, index) => (
              <ProductItem
                key={index}
                name={product.name}
                sales={product.sales}
                percentage={product.percentage}
              />
            ))}
          </View>
        </View>

        {/* Team Performance */}
        <View className="px-4 pb-6">
          <Text className="text-lg font-semibold text-gray-900 mb-4">Team Performance</Text>
          {performanceData.teamPerformance.map((member, index) => (
            <TeamMemberCard
              key={index}
              name={member.name}
              sales={member.sales}
              bonus={member.bonus}
              conversion={member.conversion}
            />
          ))}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
