import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
} from 'react-native';
import { IconSymbol } from '@/components/ui/IconSymbol';

export default function AdminPerformanceScreen() {
  const [selectedPeriod, setSelectedPeriod] = useState('weekly');

  const performanceData = {
    systemMetrics: {
      totalUsers: 18,
      activeUsers: 16,
      systemUptime: 99.8,
      dataProcessed: 15420,
    },
    rolePerformance: [
      { role: 'Sales Assistants', count: 8, avgPerformance: 85, trend: 5 },
      { role: 'Representatives', count: 5, avgPerformance: 78, trend: -2 },
      { role: 'Recruiters', count: 2, avgPerformance: 92, trend: 8 },
      { role: 'Executives', count: 2, avgPerformance: 95, trend: 3 },
    ],
  };

  const MetricCard = ({ title, value, subtitle, color = 'blue', icon, trend }) => (
    <View className="bg-white p-4 rounded-xl shadow-sm border border-gray-100 flex-1 mx-1 mb-3">
      <View className="flex-row items-center justify-between mb-2">
        <IconSymbol name={icon} size={20} color={`#${color === 'blue' ? '3b82f6' : color === 'green' ? '10b981' : color === 'purple' ? '8b5cf6' : 'f59e0b'}`} />
        {trend && (
          <View className={`px-2 py-1 rounded-full ${trend > 0 ? 'bg-green-100' : 'bg-red-100'}`}>
            <Text className={`text-xs font-medium ${trend > 0 ? 'text-green-700' : 'text-red-700'}`}>
              {trend > 0 ? '+' : ''}{trend}%
            </Text>
          </View>
        )}
      </View>
      <Text className="text-gray-600 text-sm font-medium">{title}</Text>
      <Text className={`text-xl font-bold mt-1 text-${color}-600`}>{value}</Text>
      {subtitle && <Text className="text-gray-500 text-xs mt-1">{subtitle}</Text>}
    </View>
  );

  const RolePerformanceCard = ({ role, count, avgPerformance, trend }) => (
    <View className="bg-white p-4 rounded-xl shadow-sm border border-gray-100 mb-3">
      <View className="flex-row items-center justify-between mb-3">
        <Text className="text-gray-900 font-semibold">{role}</Text>
        <View className={`px-2 py-1 rounded-full ${
          avgPerformance >= 90 ? 'bg-green-100' : avgPerformance >= 80 ? 'bg-yellow-100' : 'bg-red-100'
        }`}>
          <Text className={`text-xs font-medium ${
            avgPerformance >= 90 ? 'text-green-700' : avgPerformance >= 80 ? 'text-yellow-700' : 'text-red-700'
          }`}>
            {avgPerformance}%
          </Text>
        </View>
      </View>
      <View className="flex-row justify-between items-center">
        <View>
          <Text className="text-gray-500 text-sm">Team Size</Text>
          <Text className="text-gray-900 font-semibold">{count} members</Text>
        </View>
        <View className="items-end">
          <Text className="text-gray-500 text-sm">Trend</Text>
          <View className={`px-2 py-1 rounded-full ${trend > 0 ? 'bg-green-100' : 'bg-red-100'}`}>
            <Text className={`text-xs font-medium ${trend > 0 ? 'text-green-700' : 'text-red-700'}`}>
              {trend > 0 ? '+' : ''}{trend}%
            </Text>
          </View>
        </View>
      </View>
    </View>
  );

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <StatusBar barStyle="dark-content" backgroundColor="#f9fafb" />
      
      <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View className="bg-white px-4 py-6 border-b border-gray-100">
          <Text className="text-2xl font-bold text-gray-900 mb-2">
            System Performance
          </Text>
          <Text className="text-gray-600">Overall system and team analytics</Text>
        </View>

        {/* Period Selector */}
        <View className="px-4 py-4">
          <View className="flex-row space-x-2">
            <TouchableOpacity 
              className={`px-4 py-2 rounded-lg ${selectedPeriod === 'weekly' ? 'bg-blue-500' : 'bg-gray-100'}`}
              onPress={() => setSelectedPeriod('weekly')}
            >
              <Text className={`font-medium ${selectedPeriod === 'weekly' ? 'text-white' : 'text-gray-700'}`}>
                Weekly
              </Text>
            </TouchableOpacity>
            <TouchableOpacity 
              className={`px-4 py-2 rounded-lg ${selectedPeriod === 'monthly' ? 'bg-blue-500' : 'bg-gray-100'}`}
              onPress={() => setSelectedPeriod('monthly')}
            >
              <Text className={`font-medium ${selectedPeriod === 'monthly' ? 'text-white' : 'text-gray-700'}`}>
                Monthly
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* System Metrics */}
        <View className="px-4 pb-6">
          <Text className="text-lg font-semibold text-gray-900 mb-4">System Metrics</Text>
          <View className="flex-row mb-4">
            <MetricCard 
              title="Total Users" 
              value={performanceData.systemMetrics.totalUsers}
              subtitle="All roles"
              color="blue"
              icon="person.3"
            />
            <MetricCard 
              title="Active Users" 
              value={performanceData.systemMetrics.activeUsers}
              subtitle="Currently online"
              color="green"
              icon="person.circle"
              trend={8}
            />
          </View>
          <View className="flex-row">
            <MetricCard 
              title="System Uptime" 
              value={`${performanceData.systemMetrics.systemUptime}%`}
              subtitle="This month"
              color="purple"
              icon="server.rack"
              trend={2}
            />
            <MetricCard 
              title="Data Processed" 
              value={performanceData.systemMetrics.dataProcessed.toLocaleString()}
              subtitle="Requests today"
              color="orange"
              icon="chart.bar.doc.horizontal"
              trend={15}
            />
          </View>
        </View>

        {/* Role Performance */}
        <View className="px-4 pb-6">
          <Text className="text-lg font-semibold text-gray-900 mb-4">Role Performance</Text>
          {performanceData.rolePerformance.map((role, index) => (
            <RolePerformanceCard
              key={index}
              role={role.role}
              count={role.count}
              avgPerformance={role.avgPerformance}
              trend={role.trend}
            />
          ))}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
