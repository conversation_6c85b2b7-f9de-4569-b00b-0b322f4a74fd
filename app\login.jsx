import api from '@/api';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useUser } from '@/contexts/UserContext';
import { router } from 'expo-router';
import { useState } from 'react';
import {
    ActivityIndicator,
    Alert,
    KeyboardAvoidingView,
    Platform,
    SafeAreaView,
    StatusBar,
    Text,
    TextInput,
    TouchableOpacity,
    View,
} from 'react-native';

// Mock API configuration - Replace with your actual endpoint
const API_CONFIG = {
  baseUrl: 'http://*************:8000/api/v1/',
  loginEndpoint: 'auth/login',
  // Example: 'https://api.asshop.com/auth/login'
};

// User roles configuration
const USER_ROLES = {
  assistant: {
    name: 'Sales Assistant',
    description: 'Appointment scheduling specialist',
    dashboardRoute: '/(tabs)/salesAssistant/dashboard',
    icon: '📞',
  },
  representative: {
    name: 'Sales Representative',
    description: 'Client meeting specialist',
    dashboardRoute: '/(tabs)/representative/dashboard',
    icon: '🤝',
  },
  recruiter: {
    name: 'Recruiter',
    description: 'Team optimization specialist',
    dashboardRoute: '/(tabs)/recruiter/dashboard',
    icon: '👥',
  },
  executive: {
    name: 'Executive',
    description: 'Financial oversight manager',
    dashboardRoute: '/(tabs)/executive/dashboard',
    icon: '💼',
  },
  admin: {
    name: 'Administrator',
    description: 'System management',
    dashboardRoute: '/(tabs)/admin/dashboard',
    icon: '⚙️',
  },
};

export default function LoginScreen() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const { saveUser } = useUser();

  // Login function using API
  const handleLogin = async () => {
    if (!email || !password) {
      Alert.alert('Error', 'Please enter both email and password');
      return;
    }

    setIsLoading(true);

    try {
      // Use API login function
      const response = await api.login(email.toLowerCase().trim(), password);

      if (response && response.data) {
        const data = response.data.data;
        console.log("succeful response : ",data)
        // Successful login
        const userRole = data.user?.role || 'assistant'; // Default to assistant
        const roleConfig = USER_ROLES[userRole];

        if (roleConfig) {
          console.log(data.user);
          // Save user data to context and storage
          await saveUser(data.user, data.token);

          Alert.alert(
            'Login Successful',
            `Welcome ${data.user?.name || 'User'}!\nRole: ${roleConfig.name}`,
            [
              {
                text: 'Continue',
                onPress: () => {
                  // Navigate to role-specific dashboard
                  router.replace(roleConfig.dashboardRoute);
                },
              },
            ]
          );
        } else {
          Alert.alert('Error', 'Invalid user role');
        }
      } else {
        // Login failed
        Alert.alert('Login Failed', 'Invalid credentials or server error');
      }
    } catch (error) {
      // Network or other error
      console.error('Login error:', error);

      // For development - Mock successful login
      Alert.alert(
        'Development Mode',
        'API endpoint not configured. Choose a role to continue:',
        [
          {
            text: 'Assistant',
            onPress: async () => {
              await saveUser({ id: 1, name: 'Test Assistant', role: 'assistant', email: '<EMAIL>' });
              router.replace('/(tabs)/salesAssistant/dashboard');
            }
          },
          {
            text: 'Representative',
            onPress: async () => {
              await saveUser({ id: 2, name: 'Test Representative', role: 'representative', email: '<EMAIL>' });
              router.replace('/(tabs)/representative/dashboard');
            }
          },
          {
            text: 'Recruiter',
            onPress: async () => {
              await saveUser({ id: 3, name: 'Test Recruiter', role: 'recruiter', email: '<EMAIL>' });
              router.replace('/(tabs)/recruiter/dashboard');
            }
          },
          {
            text: 'Executive',
            onPress: async () => {
              await saveUser({ id: 4, name: 'Test Executive', role: 'executive', email: '<EMAIL>' });
              router.replace('/(tabs)/executive/dashboard');
            }
          },
          {
            text: 'Admin',
            onPress: async () => {
              await saveUser({ id: 5, name: 'Test Admin', role: 'admin', email: '<EMAIL>' });
              router.replace('/(tabs)/admin/dashboard');
            }
          },
          { text: 'Cancel', style: 'cancel' },
        ]
      );
    } finally {
      setIsLoading(false);
    }
  };

  // const RoleCard = ({ roleKey, roleData }) => (
  //   <View className="bg-gray-50 p-3 rounded-lg mb-2">
  //     <View className="flex-row items-center">
  //       <Text className="text-2xl mr-3">{roleData.icon}</Text>
  //       <View className="flex-1">
  //         <Text className="text-gray-900 font-semibold">{roleData.name}</Text>
  //         <Text className="text-gray-600 text-sm">{roleData.description}</Text>
  //       </View>
  //     </View>
  //   </View>
  // );

  return (
    <SafeAreaView className="flex-1 bg-white">
      <StatusBar barStyle="dark-content" backgroundColor="white" />
      
      <KeyboardAvoidingView 
        className="flex-1" 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <View className="flex-1 px-6 justify-center">
          {/* Logo and Title */}
          <View className="items-center mb-8">
            <View className="w-20 h-20 bg-blue-500 rounded-full items-center justify-center mb-4">
              <Text className="text-white text-2xl font-bold">AS</Text>
            </View>
            <Text className="text-3xl font-bold text-gray-900 mb-2">AS-Shop</Text>
            <Text className="text-gray-600 text-center">
              Commercial Management Solution
            </Text>
          </View>

          {/* Login Form */}
          <View className="mb-6">
            <Text className="text-gray-700 font-medium mb-2">Email</Text>
            <View className="bg-gray-50 border border-gray-200 rounded-lg px-4 py-3 mb-4">
              <TextInput
                className="text-gray-900"
                placeholder="Enter your email"
                placeholderTextColor="#9ca3af"
                value={email}
                onChangeText={setEmail}
                keyboardType="email-address"
                autoCapitalize="none"
                autoCorrect={false}
              />
            </View>

            <Text className="text-gray-700 font-medium mb-2">Password</Text>
            <View className="bg-gray-50 border border-gray-200 rounded-lg px-4 py-3 mb-6 flex-row items-center">
              <TextInput
                className="flex-1 text-gray-900"
                placeholder="Enter your password"
                placeholderTextColor="#9ca3af"
                value={password}
                onChangeText={setPassword}
                secureTextEntry={!showPassword}
                autoCapitalize="none"
                autoCorrect={false}
              />
              <TouchableOpacity
                onPress={() => setShowPassword(!showPassword)}
                className="ml-2"
              >
                <IconSymbol 
                  name={showPassword ? "eye.slash" : "eye"} 
                  size={20} 
                  color="#6b7280" 
                />
              </TouchableOpacity>
            </View>

            {/* Login Button */}
            <TouchableOpacity
              className={`bg-blue-500 rounded-lg py-4 items-center ${isLoading ? 'opacity-50' : ''}`}
              onPress={handleLogin}
              disabled={isLoading}
            >
              {isLoading ? (
                <ActivityIndicator color="white" />
              ) : (
                <Text className="text-white font-semibold text-lg">Sign In</Text>
              )}
            </TouchableOpacity>
          </View>

          {/* User Roles Information */}
          {/* <View className="mt-6">
            <Text className="text-gray-700 font-semibold mb-3 text-center">
              Available User Roles
            </Text>
            {Object.entries(USER_ROLES).map(([key, role]) => (
              <RoleCard key={key} roleKey={key} roleData={role} />
            ))}
          </View> */}

          {/* API Configuration Note */}
          {/* <View className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <Text className="text-yellow-800 text-sm font-medium mb-1">
              🔧 API Configuration Required
            </Text>
            <Text className="text-yellow-700 text-xs">
              Update API_CONFIG.baseUrl in login.tsx with your endpoint
            </Text>
            <Text className="text-yellow-700 text-xs mt-1">
              Current: {API_CONFIG.baseUrl}
            </Text>
          </View> */}
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}
