import { IconSymbol } from '@/components/ui/IconSymbol';
import { router, useLocalSearchParams } from 'expo-router';
import React from 'react';
import {
    SafeAreaView,
    ScrollView,
    StatusBar,
    Text,
    TextInput,
    TouchableOpacity,
    View,
} from 'react-native';

// Mock detailed appointment data
const mockAppointmentDetails = {
  '1': {
    id: '1',
    clientName: '<PERSON>',
    clientEmail: '<EMAIL>',
    clientPhone: '+****************',
    date: 'July 24, 2024',
    time: '9:00 AM',
    duration: '1 hour',
    status: 'confirmed',
    avatar: '👩‍💼',
    appointmentType: 'Consultation',
    description: 'Initial consultation for new project requirements and timeline discussion.',
    weight: '65 kg',
    purchasePrice: '€150',
    estimatedValue: '€200',
    isComplete: false,
    individualResult: '€50',
  },
  '2': {
    id: '2',
    clientName: '<PERSON>',
    clientEmail: '<EMAIL>',
    clientPhone: '+****************',
    date: 'July 24, 2024',
    time: '2:00 PM',
    duration: '1 hour',
    status: 'confirmed',
    avatar: '👨‍💼',
    appointmentType: 'Follow-up',
    description: 'Follow-up meeting to review progress and discuss next steps.',
    weight: '75 kg',
    purchasePrice: '€200',
    estimatedValue: '€250',
    isComplete: false,
    individualResult: '€75',
  },
  // Add more mock data for other appointments...
};

export default function AppointmentDetailsScreen() {
  const { id } = useLocalSearchParams();
  const appointment = mockAppointmentDetails[id] || mockAppointmentDetails['1'];

  const FormField = ({ label, value, placeholder, multiline = false }) => (
    <View className="mb-4">
      <Text className="text-gray-700 font-medium mb-2">{label}</Text>
      <View className={`bg-gray-50 border border-gray-200 rounded-lg px-3 ${multiline ? 'py-3' : 'py-2'}`}>
        <TextInput
          className="text-gray-900"
          value={value}
          placeholder={placeholder}
          placeholderTextColor="#9ca3af"
          multiline={multiline}
          numberOfLines={multiline ? 3 : 1}
          editable={false}
        />
      </View>
    </View>
  );

  return (
    <SafeAreaView className="flex-1 bg-white">
      <StatusBar barStyle="dark-content" backgroundColor="white" />

      {/* Header */}
      <View className="flex-row items-center justify-between px-4 py-4 border-b border-gray-100">
        <TouchableOpacity
          onPress={() => router.back()}
          className="w-10 h-10 items-center justify-center"
        >
          <IconSymbol name="chevron.left" size={20} color="#374151" />
        </TouchableOpacity>
        <Text className="text-lg font-semibold text-gray-900">Appointment Details</Text>
        <View className="w-10" />
      </View>

      <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
        {/* Client Information */}
        <View className="px-4 py-6 border-b border-gray-100">
          <Text className="text-lg font-semibold text-gray-900 mb-4">Client Information</Text>
          <View className="flex-row items-center">
            <View className="w-12 h-12 bg-blue-100 rounded-full items-center justify-center mr-4">
              <Text className="text-xl">{appointment.avatar}</Text>
            </View>
            <View>
              <Text className="text-gray-900 font-medium text-base">{appointment.clientName}</Text>
              <Text className="text-gray-500 text-sm">{appointment.clientEmail}</Text>
              <Text className="text-gray-500 text-sm">{appointment.clientPhone}</Text>
            </View>
          </View>
        </View>

        {/* Appointment Details */}
        <View className="px-4 py-6 border-b border-gray-100">
          <Text className="text-lg font-semibold text-gray-900 mb-4">Appointment Details</Text>
          <View className="space-y-3">
            <View className="flex-row items-center">
              <IconSymbol name="calendar" size={16} color="#6b7280" />
              <Text className="text-gray-900 ml-3">{appointment.date}</Text>
            </View>
            <View className="flex-row items-center">
              <IconSymbol name="clock" size={16} color="#6b7280" />
              <Text className="text-gray-900 ml-3">{appointment.time}</Text>
            </View>
            <View className="flex-row items-center">
              <IconSymbol name="hourglass" size={16} color="#6b7280" />
              <Text className="text-gray-900 ml-3">{appointment.duration}</Text>
            </View>
          </View>
        </View>

        {/* Post-Appointment Form */}
        <View className="px-4 py-6">
          <Text className="text-lg font-semibold text-gray-900 mb-4">Post-Appointment Form</Text>

          <FormField
            label="Item Type"
            value={appointment.appointmentType}
            placeholder="Enter item type"
          />

          <FormField
            label="Description"
            value={appointment.description}
            placeholder="Enter description"
            multiline={true}
          />

          <FormField
            label="Weight (grams)"
            value={appointment.weight}
            placeholder="Enter weight"
          />

          <FormField
            label="Purchase Price (€)"
            value={appointment.purchasePrice}
            placeholder="Enter purchase price"
          />

          <FormField
            label="Estimated Value (€)"
            value={appointment.estimatedValue}
            placeholder="Enter estimated value"
          />

          {/* Appointment Complete Toggle */}
          <View className="flex-row items-center justify-between mb-6">
            <Text className="text-gray-700 font-medium">Appointment Complete</Text>
            <TouchableOpacity
              className={`w-12 h-6 rounded-full ${appointment.isComplete ? 'bg-green-500' : 'bg-gray-300'}`}
            >
              <View className={`w-5 h-5 bg-white rounded-full mt-0.5 ${appointment.isComplete ? 'ml-6' : 'ml-0.5'}`} />
            </TouchableOpacity>
          </View>

          {/* Individual Result */}
          <FormField
            label="Individual Result"
            value={appointment.individualResult}
            placeholder="Enter result"
          />
        </View>
      </ScrollView>

      {/* Bottom Action Buttons */}
      <View className="px-4 py-4 border-t border-gray-100 bg-white">
        <View className="flex-row space-x-3">
          <TouchableOpacity className="flex-1 bg-gray-100 py-3 rounded-lg items-center">
            <IconSymbol name="phone" size={20} color="#374151" />
          </TouchableOpacity>
          <TouchableOpacity className="flex-1 bg-gray-100 py-3 rounded-lg items-center">
            <IconSymbol name="message" size={20} color="#374151" />
          </TouchableOpacity>
          <TouchableOpacity className="flex-1 bg-gray-100 py-3 rounded-lg items-center">
            <IconSymbol name="calendar" size={20} color="#374151" />
          </TouchableOpacity>
          <TouchableOpacity className="flex-1 bg-gray-100 py-3 rounded-lg items-center">
            <IconSymbol name="magnifyingglass" size={20} color="#374151" />
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
}