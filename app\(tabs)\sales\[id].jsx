import React from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
} from 'react-native';
import { useLocalSearchParams, router } from 'expo-router';
import { IconSymbol } from '@/components/ui/IconSymbol';

// Mock detailed sales data
const mockSalesDetails = {
  '1': {
    id: '1',
    clientName: '<PERSON>',
    clientEmail: '<EMAIL>',
    appointmentId: 'APT-001',
    date: '2024-07-24',
    time: '09:00 AM',
    purchaseAmount: 250,
    profit: 75,
    profitMargin: 30,
    conversionRate: 100,
    status: 'completed',
    itemDetails: {
      type: 'Gold Ring',
      weight: '15g',
      purity: '18K',
      condition: 'Excellent',
      estimatedValue: 280,
      finalPrice: 250,
    },
    bonusCalculation: {
      baseBonus: 25,
      performanceBonus: 15,
      teamBonus: 10,
      totalBonus: 50,
    },
    timeline: [
      { step: 'Appointment Scheduled', time: '2024-07-23 14:30', completed: true },
      { step: 'Client Consultation', time: '2024-07-24 09:00', completed: true },
      { step: 'Item Evaluation', time: '2024-07-24 09:15', completed: true },
      { step: 'Price Negotiation', time: '2024-07-24 09:30', completed: true },
      { step: 'Purchase Completed', time: '2024-07-24 09:45', completed: true },
    ],
  },
  '2': {
    id: '2',
    clientName: 'John Smith',
    clientEmail: '<EMAIL>',
    appointmentId: 'APT-002',
    date: '2024-07-24',
    time: '02:00 PM',
    purchaseAmount: 180,
    profit: 45,
    profitMargin: 25,
    conversionRate: 100,
    status: 'completed',
    itemDetails: {
      type: 'Silver Necklace',
      weight: '25g',
      purity: '925 Sterling',
      condition: 'Good',
      estimatedValue: 200,
      finalPrice: 180,
    },
    bonusCalculation: {
      baseBonus: 18,
      performanceBonus: 12,
      teamBonus: 8,
      totalBonus: 38,
    },
    timeline: [
      { step: 'Appointment Scheduled', time: '2024-07-23 16:00', completed: true },
      { step: 'Client Consultation', time: '2024-07-24 14:00', completed: true },
      { step: 'Item Evaluation', time: '2024-07-24 14:20', completed: true },
      { step: 'Price Negotiation', time: '2024-07-24 14:35', completed: true },
      { step: 'Purchase Completed', time: '2024-07-24 14:50', completed: true },
    ],
  },
};

export default function SaleDetailsScreen() {
  const { id } = useLocalSearchParams();
  const sale = mockSalesDetails[id] || mockSalesDetails['1'];

  const StatCard = ({ title, value, subtitle, color = 'blue' }) => (
    <View className="bg-white p-4 rounded-xl shadow-sm border border-gray-100 flex-1 mx-1">
      <Text className="text-gray-600 text-sm font-medium">{title}</Text>
      <Text className={`text-xl font-bold mt-1 text-${color}-600`}>{value}</Text>
      {subtitle && <Text className="text-gray-500 text-xs mt-1">{subtitle}</Text>}
    </View>
  );

  const TimelineItem = ({ step, time, completed }) => (
    <View className="flex-row items-center py-3">
      <View className={`w-4 h-4 rounded-full mr-4 ${completed ? 'bg-green-500' : 'bg-gray-300'}`} />
      <View className="flex-1">
        <Text className="text-gray-900 font-medium text-sm">{step}</Text>
        <Text className="text-gray-500 text-xs mt-1">{time}</Text>
      </View>
      {completed && <IconSymbol name="checkmark" size={16} color="#10b981" />}
    </View>
  );

  return (
    <SafeAreaView className="flex-1 bg-white">
      <StatusBar barStyle="dark-content" backgroundColor="white" />
      
      {/* Header */}
      <View className="flex-row items-center justify-between px-4 py-4 border-b border-gray-100">
        <TouchableOpacity 
          onPress={() => router.back()}
          className="w-10 h-10 items-center justify-center"
        >
          <IconSymbol name="chevron.left" size={20} color="#374151" />
        </TouchableOpacity>
        <Text className="text-lg font-semibold text-gray-900">Sale Details</Text>
        <TouchableOpacity className="w-10 h-10 items-center justify-center">
          <IconSymbol name="ellipsis" size={20} color="#374151" />
        </TouchableOpacity>
      </View>

      <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
        {/* Client & Sale Info */}
        <View className="px-4 py-6 border-b border-gray-100">
          <View className="flex-row items-center justify-between mb-4">
            <View>
              <Text className="text-xl font-bold text-gray-900">{sale.clientName}</Text>
              <Text className="text-gray-500">{sale.appointmentId} • {sale.date}</Text>
            </View>
            <View className={`px-3 py-1 rounded-full ${
              sale.status === 'completed' ? 'bg-green-100' : 'bg-red-100'
            }`}>
              <Text className={`text-sm font-medium ${
                sale.status === 'completed' ? 'text-green-700' : 'text-red-700'
              }`}>
                {sale.status === 'completed' ? 'Completed' : 'No Sale'}
              </Text>
            </View>
          </View>

          {/* Key Metrics */}
          <View className="flex-row mb-4">
            <StatCard 
              title="Purchase Amount" 
              value={`€${sale.purchaseAmount}`}
              color="green"
            />
            <StatCard 
              title="Profit" 
              value={`€${sale.profit}`}
              subtitle={`${sale.profitMargin}% margin`}
              color="blue"
            />
          </View>
        </View>

        {/* Item Details */}
        <View className="px-4 py-6 border-b border-gray-100">
          <Text className="text-lg font-semibold text-gray-900 mb-4">Item Details</Text>
          <View className="bg-gray-50 p-4 rounded-lg">
            <View className="flex-row justify-between items-center mb-2">
              <Text className="text-gray-600">Type:</Text>
              <Text className="text-gray-900 font-medium">{sale.itemDetails.type}</Text>
            </View>
            <View className="flex-row justify-between items-center mb-2">
              <Text className="text-gray-600">Weight:</Text>
              <Text className="text-gray-900 font-medium">{sale.itemDetails.weight}</Text>
            </View>
            <View className="flex-row justify-between items-center mb-2">
              <Text className="text-gray-600">Purity:</Text>
              <Text className="text-gray-900 font-medium">{sale.itemDetails.purity}</Text>
            </View>
            <View className="flex-row justify-between items-center mb-2">
              <Text className="text-gray-600">Condition:</Text>
              <Text className="text-gray-900 font-medium">{sale.itemDetails.condition}</Text>
            </View>
            <View className="flex-row justify-between items-center">
              <Text className="text-gray-600">Estimated Value:</Text>
              <Text className="text-gray-900 font-medium">€{sale.itemDetails.estimatedValue}</Text>
            </View>
          </View>
        </View>

        {/* Bonus Calculation */}
        <View className="px-4 py-6 border-b border-gray-100">
          <Text className="text-lg font-semibold text-gray-900 mb-4">Bonus Calculation</Text>
          <View className="bg-blue-50 p-4 rounded-lg">
            <View className="flex-row justify-between items-center mb-2">
              <Text className="text-gray-600">Base Bonus:</Text>
              <Text className="text-gray-900 font-medium">€{sale.bonusCalculation.baseBonus}</Text>
            </View>
            <View className="flex-row justify-between items-center mb-2">
              <Text className="text-gray-600">Performance Bonus:</Text>
              <Text className="text-gray-900 font-medium">€{sale.bonusCalculation.performanceBonus}</Text>
            </View>
            <View className="flex-row justify-between items-center mb-3">
              <Text className="text-gray-600">Team Bonus:</Text>
              <Text className="text-gray-900 font-medium">€{sale.bonusCalculation.teamBonus}</Text>
            </View>
            <View className="border-t border-blue-200 pt-3">
              <View className="flex-row justify-between items-center">
                <Text className="text-blue-700 font-semibold">Total Bonus:</Text>
                <Text className="text-blue-700 font-bold text-lg">€{sale.bonusCalculation.totalBonus}</Text>
              </View>
            </View>
          </View>
        </View>

        {/* Timeline */}
        <View className="px-4 py-6">
          <Text className="text-lg font-semibold text-gray-900 mb-4">Sale Timeline</Text>
          <View className="bg-white">
            {sale.timeline.map((item, index) => (
              <TimelineItem
                key={index}
                step={item.step}
                time={item.time}
                completed={item.completed}
              />
            ))}
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
