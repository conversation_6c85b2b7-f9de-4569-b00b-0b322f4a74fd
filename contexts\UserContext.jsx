import AsyncStorage from "@react-native-async-storage/async-storage";
import { router } from "expo-router";
import React, { createContext, useContext, useEffect, useState } from "react";
import APP_URL from '@/api/index'
const UserContext = createContext();

export const useUser = () => {
  const context = useContext(UserContext);
  if (!context) {
    throw new Error("useUser must be used within a UserProvider");
  }
  return context;
};

export const UserProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [authToken, setAuthToken] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [loadingRankings, setLoadingRankings] = useState(false);
  const [rankings, setRankings] = useState([]);
  const [weekInfo, setWeekInfo] = useState(null);

  // Load user data from storage on app start
  useEffect(() => {
    loadUserData();
    fetchWeeklyRankings();
  }, []);

  const loadUserData = async () => {
    try {
      const userData = await AsyncStorage.getItem("userData");
      const token = await AsyncStorage.getItem("userToken");
      console.log("User data loaded:", userData, "Token:", token);
      if (userData) {
        setUser(JSON.parse(userData));
      }
      if (token) {
        setAuthToken(token);
      }
    } catch (error) {
      console.error("Error loading user data:", error);
    } finally {
      setIsLoading(false);
    }
  };
  const fetchWeeklyRankings = async () => {
    if (!authToken) return;
    setLoadingRankings(true);
    try {
      const response = await fetch(
        `${APP_URL.APP_URL}/representatives/weekly-rankings`,
        {
          headers: {
            Authorization: `Bearer ${authToken}`,
            "Content-Type": "application/json",
          },
        }
      );

      const data = await response.json();
      console.log("Rankings data:", data);
      if (data.success) {
        setRankings(data.data.rankings);
        setWeekInfo(data.data.meta.week_period);
      }
    } catch (error) {
      console.error("Error fetching rankings:", error);
    } finally {
      setLoadingRankings(false);
    }
  };

  const saveUser = async (userData, token = null) => {
    try {
      await AsyncStorage.setItem("userData", JSON.stringify(userData));
      setUser(userData);
      if (token) {
        await AsyncStorage.setItem("userToken", token);
        setAuthToken(token);
      }
    } catch (error) {
      console.error("Error saving user data:", error);
    }
  };

  const logout = async () => {
    try {
      await AsyncStorage.removeItem("userData");
      await AsyncStorage.removeItem("userToken");
      setUser(null);
      setAuthToken(null);
      router.replace("/login");
    } catch (error) {
      console.error("Error during logout:", error);
    }
  };

  const updateUser = async (updatedData) => {
    try {
      const newUserData = { ...user, ...updatedData };
      await AsyncStorage.setItem("userData", JSON.stringify(newUserData));
      setUser(newUserData);
    } catch (error) {
      console.error("Error updating user data:", error);
    }
  };

  const value = {
    user,
    authToken,
    isLoading,
    saveUser,
    logout,
    updateUser,
    loadingRankings,
    rankings,
    weekInfo,
    fetchWeeklyRankings,
    isLoggedIn: !!user,
  };

  return <UserContext.Provider value={value}>{children}</UserContext.Provider>;
};
