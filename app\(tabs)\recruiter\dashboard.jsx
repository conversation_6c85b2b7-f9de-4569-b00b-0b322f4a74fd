import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
} from 'react-native';
import { router } from 'expo-router';
import { IconSymbol } from '@/components/ui/IconSymbol';

// Mock data for Recruiter
const recruiterData = {
  user: {
    name: '<PERSON>',
    role: 'Recruiter',
    avatar: '👥',
  },
  teamOverview: {
    totalAssistants: 8,
    totalRepresentatives: 5,
    activePairings: 12,
    avgCollaborationScore: 85,
  },
  topPairings: [
    {
      assistant: '<PERSON>',
      representative: '<PERSON>',
      collaborationScore: 92,
      weeklyAppointments: 18,
      conversionRate: 83,
      revenue: 2400,
    },
    {
      assistant: '<PERSON>',
      representative: '<PERSON>',
      collaborationScore: 88,
      weeklyAppointments: 15,
      conversionRate: 80,
      revenue: 2100,
    },
    {
      assistant: '<PERSON>',
      representative: '<PERSON>',
      collaborationScore: 85,
      weeklyAppointments: 14,
      conversionRate: 75,
      revenue: 1950,
    },
  ],
  performanceMetrics: {
    assistantPerformance: [
      { name: '<PERSON>', appointments: 24, validationRate: 85, ranking: 1 },
      { name: '<PERSON>', appointments: 22, validationRate: 82, ranking: 2 },
      { name: '<PERSON>', appointments: 19, validationRate: 78, ranking: 3 },
    ],
    representativePerformance: [
      { name: '<PERSON>', conversionRate: 83, revenue: 2400, ranking: 1 },
      { name: 'Sophie Chen', conversionRate: 80, revenue: 2100, ranking: 2 },
      { name: 'Thomas Blanc', conversionRate: 75, revenue: 1950, ranking: 3 },
    ],
  },
  quickActions: [
    { id: 1, title: 'Pairing Analysis', icon: 'person.2.circle', route: '/recruiter/pairings' },
    { id: 2, title: 'Team Performance', icon: 'chart.bar', route: '/recruiter/performance' },
    { id: 3, title: 'Optimization', icon: 'gear', route: '/recruiter/optimization' },
  ],
  alerts: [
    { id: 1, type: 'suggestion', message: 'Consider pairing Lisa Chen with Alex Martin for better results', priority: 'medium' },
    { id: 2, type: 'warning', message: 'Thomas Blanc conversion rate below target (75%)', priority: 'high' },
  ],
};

export default function RecruiterDashboardScreen() {
  const [selectedView, setSelectedView] = useState('pairings');

  const MetricCard = ({ title, value, subtitle, color = 'blue', icon }) => (
    <View className="bg-white p-4 rounded-xl shadow-sm border border-gray-100 flex-1 mx-1">
      <View className="flex-row items-center justify-between mb-2">
        <IconSymbol name={icon} size={20} color={`#${color === 'blue' ? '3b82f6' : color === 'green' ? '10b981' : color === 'purple' ? '8b5cf6' : 'f59e0b'}`} />
      </View>
      <Text className="text-gray-600 text-sm font-medium">{title}</Text>
      <Text className={`text-2xl font-bold mt-1 text-${color}-600`}>{value}</Text>
      {subtitle && <Text className="text-gray-500 text-xs mt-1">{subtitle}</Text>}
    </View>
  );

  const PairingCard = ({ assistant, representative, collaborationScore, weeklyAppointments, conversionRate, revenue }) => (
    <View className="bg-white p-4 rounded-xl shadow-sm border border-gray-100 mb-3">
      <View className="flex-row items-center justify-between mb-3">
        <View className="flex-1">
          <Text className="text-gray-900 font-semibold">{assistant} ↔ {representative}</Text>
          <Text className="text-gray-500 text-sm">Assistant ↔ Representative</Text>
        </View>
        <View className={`px-3 py-1 rounded-full ${
          collaborationScore >= 90 ? 'bg-green-100' : collaborationScore >= 80 ? 'bg-yellow-100' : 'bg-red-100'
        }`}>
          <Text className={`text-sm font-medium ${
            collaborationScore >= 90 ? 'text-green-700' : collaborationScore >= 80 ? 'text-yellow-700' : 'text-red-700'
          }`}>
            {collaborationScore}%
          </Text>
        </View>
      </View>
      <View className="flex-row justify-between">
        <View className="flex-1">
          <Text className="text-gray-500 text-xs">Weekly Appointments</Text>
          <Text className="text-gray-900 font-semibold">{weeklyAppointments}</Text>
        </View>
        <View className="flex-1">
          <Text className="text-gray-500 text-xs">Conversion Rate</Text>
          <Text className="text-blue-600 font-semibold">{conversionRate}%</Text>
        </View>
        <View className="flex-1">
          <Text className="text-gray-500 text-xs">Revenue</Text>
          <Text className="text-green-600 font-semibold">€{revenue}</Text>
        </View>
      </View>
    </View>
  );

  const PerformanceItem = ({ name, metric1, metric2, ranking, type }) => (
    <View className="flex-row items-center justify-between py-3 border-b border-gray-100">
      <View className="flex-row items-center flex-1">
        <View className={`w-8 h-8 rounded-full items-center justify-center mr-3 ${
          ranking === 1 ? 'bg-yellow-100' : ranking === 2 ? 'bg-gray-100' : 'bg-orange-100'
        }`}>
          <Text className={`font-bold text-sm ${
            ranking === 1 ? 'text-yellow-600' : ranking === 2 ? 'text-gray-600' : 'text-orange-600'
          }`}>
            {ranking}
          </Text>
        </View>
        <View className="flex-1">
          <Text className="text-gray-900 font-medium">{name}</Text>
          <Text className="text-gray-500 text-sm">
            {type === 'assistant' ? `${metric1} appointments • ${metric2}% validation` : `${metric1}% conversion • €${metric2} revenue`}
          </Text>
        </View>
      </View>
    </View>
  );

  const QuickActionCard = ({ title, icon, onPress }) => (
    <TouchableOpacity
      className="bg-white p-4 rounded-xl shadow-sm border border-gray-100 items-center justify-center h-24 flex-1 mx-1"
      onPress={onPress}
    >
      <IconSymbol name={icon} size={24} color="#3b82f6" />
      <Text className="text-gray-700 text-sm font-medium mt-2 text-center">{title}</Text>
    </TouchableOpacity>
  );

  const AlertItem = ({ type, message, priority }) => (
    <View className={`p-3 rounded-lg mb-2 border-l-4 ${
      type === 'suggestion' ? 'bg-blue-50 border-blue-500' : 'bg-yellow-50 border-yellow-500'
    }`}>
      <View className="flex-row items-center">
        <IconSymbol 
          name={type === 'suggestion' ? 'lightbulb' : 'exclamationmark.triangle'} 
          size={16} 
          color={type === 'suggestion' ? '#3b82f6' : '#f59e0b'} 
        />
        <Text className="text-gray-900 text-sm font-medium ml-2 flex-1">{message}</Text>
        <View className={`px-2 py-1 rounded-full ${
          priority === 'high' ? 'bg-red-100' : 'bg-yellow-100'
        }`}>
          <Text className={`text-xs font-medium ${
            priority === 'high' ? 'text-red-700' : 'text-yellow-700'
          }`}>
            {priority}
          </Text>
        </View>
      </View>
    </View>
  );

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <StatusBar barStyle="dark-content" backgroundColor="#f9fafb" />
      
      <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View className="bg-white px-4 py-6 border-b border-gray-100">
          <View className="flex-row items-center justify-between mb-6">
            <View>
              <Text className="text-2xl font-bold text-gray-900">
                Team Optimization
              </Text>
              <Text className="text-gray-600 mt-1">Pairing Analytics & Performance</Text>
            </View>
            <TouchableOpacity 
              className="w-12 h-12 bg-blue-100 rounded-full items-center justify-center"
              onPress={() => router.push('/profile')}
            >
              <Text className="text-xl">{recruiterData.user.avatar}</Text>
            </TouchableOpacity>
          </View>

          {/* Team Overview Stats */}
          <View className="flex-row mb-4">
            <MetricCard 
              title="Assistants" 
              value={recruiterData.teamOverview.totalAssistants}
              subtitle="Active team members"
              color="blue"
              icon="person.circle"
            />
            <MetricCard 
              title="Representatives" 
              value={recruiterData.teamOverview.totalRepresentatives}
              subtitle="Sales specialists"
              color="green"
              icon="person.badge.plus"
            />
          </View>
          <View className="flex-row">
            <MetricCard 
              title="Active Pairings" 
              value={recruiterData.teamOverview.activePairings}
              subtitle="Current collaborations"
              color="purple"
              icon="person.2.circle"
            />
            <MetricCard 
              title="Avg Collaboration" 
              value={`${recruiterData.teamOverview.avgCollaborationScore}%`}
              subtitle="Team synergy"
              color="orange"
              icon="chart.line.uptrend.xyaxis"
            />
          </View>
        </View>

        {/* Quick Actions */}
        <View className="px-4 py-6">
          <Text className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</Text>
          <View className="flex-row">
            {recruiterData.quickActions.map((action) => (
              <QuickActionCard
                key={action.id}
                title={action.title}
                icon={action.icon}
                onPress={() => router.push(action.route)}
              />
            ))}
          </View>
        </View>

        {/* View Selector */}
        <View className="px-4 mb-4">
          <View className="flex-row space-x-2">
            <TouchableOpacity 
              className={`px-4 py-2 rounded-lg ${selectedView === 'pairings' ? 'bg-blue-500' : 'bg-gray-100'}`}
              onPress={() => setSelectedView('pairings')}
            >
              <Text className={`font-medium ${selectedView === 'pairings' ? 'text-white' : 'text-gray-700'}`}>
                Top Pairings
              </Text>
            </TouchableOpacity>
            <TouchableOpacity 
              className={`px-4 py-2 rounded-lg ${selectedView === 'performance' ? 'bg-blue-500' : 'bg-gray-100'}`}
              onPress={() => setSelectedView('performance')}
            >
              <Text className={`font-medium ${selectedView === 'performance' ? 'text-white' : 'text-gray-700'}`}>
                Individual Performance
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Content based on selected view */}
        {selectedView === 'pairings' ? (
          <View className="px-4 pb-6">
            <Text className="text-lg font-semibold text-gray-900 mb-4">Top Performing Pairings</Text>
            {recruiterData.topPairings.map((pairing, index) => (
              <PairingCard
                key={index}
                assistant={pairing.assistant}
                representative={pairing.representative}
                collaborationScore={pairing.collaborationScore}
                weeklyAppointments={pairing.weeklyAppointments}
                conversionRate={pairing.conversionRate}
                revenue={pairing.revenue}
              />
            ))}
          </View>
        ) : (
          <View className="px-4 pb-6">
            <View className="mb-6">
              <Text className="text-lg font-semibold text-gray-900 mb-4">Assistant Performance</Text>
              <View className="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
                {recruiterData.performanceMetrics.assistantPerformance.map((assistant, index) => (
                  <PerformanceItem
                    key={index}
                    name={assistant.name}
                    metric1={assistant.appointments}
                    metric2={assistant.validationRate}
                    ranking={assistant.ranking}
                    type="assistant"
                  />
                ))}
              </View>
            </View>
            
            <View>
              <Text className="text-lg font-semibold text-gray-900 mb-4">Representative Performance</Text>
              <View className="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
                {recruiterData.performanceMetrics.representativePerformance.map((rep, index) => (
                  <PerformanceItem
                    key={index}
                    name={rep.name}
                    metric1={rep.conversionRate}
                    metric2={rep.revenue}
                    ranking={rep.ranking}
                    type="representative"
                  />
                ))}
              </View>
            </View>
          </View>
        )}

        {/* Optimization Alerts */}
        <View className="px-4 pb-6">
          <Text className="text-lg font-semibold text-gray-900 mb-4">Optimization Alerts</Text>
          {recruiterData.alerts.map((alert) => (
            <AlertItem
              key={alert.id}
              type={alert.type}
              message={alert.message}
              priority={alert.priority}
            />
          ))}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
