import { IconSymbol } from '@/components/ui/IconSymbol';
import { useAppointments } from '@/contexts/AppointmentsContext';
import { useUser } from '@/contexts/UserContext';
import { router } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
    ActivityIndicator,
    Alert,
    SafeAreaView,
    ScrollView,
    StatusBar,
    Text,
    TouchableOpacity,
    View
} from 'react-native';

// Mock data for Sales Representative
const representativeData = {
  user: {
    name: '<PERSON>',
    role: 'Sales Representative',
    avatar: '🤝',
  },
  todayStats: {
    scheduledAppointments: 5,
    completedAppointments: 3,
    totalRevenue: 850,
    conversionRate: 80,
  },
  upcomingAppointments: [
    { 
      id: 1, 
      client: '<PERSON>', 
      time: '14:00', 
      assistant: '<PERSON>',
      type: 'announced',
      estimatedValue: 200,
      address: '15 Rue de la Paix, Paris'
    },
    { 
      id: 2, 
      client: '<PERSON>', 
      time: '16:30', 
      assistant: '<PERSON>',
      type: 'not_announced',
      estimatedValue: 350,
      address: '8 Avenue des Champs, Lyon'
    },
  ],
  recentSales: [
    { 
      id: 1, 
      client: '<PERSON>', 
      amount: 450, 
      profit: 135, 
      time: '11:00',
      items: 'Gold necklace, Silver ring'
    },
    { 
      id: 2, 
      client: 'Jean Dupont', 
      amount: 280, 
      profit: 84, 
      time: '09:30',
      items: 'Vintage watch'
    },
  ],
  weeklyPerformance: {
    appointmentsCompleted: 18,
    salesMade: 14,
    totalRevenue: 4200,
    totalProfit: 1260,
    conversionRate: 78,
    avgSaleValue: 300,
  },
  quickActions: [
    { id: 1, title: 'View Calendar', icon: 'calendar', route: '/representative/calendar' },
    { id: 2, title: 'Sales History', icon: 'clock.arrow.circlepath', route: '/representative/history' },
    { id: 3, title: 'Performance', icon: 'chart.bar', route: '/representative/performance' },
  ],
};

export default function RepresentativeDashboardScreen() {
  const { user, logout } = useUser();
  const {
    appointments,
    isLoading,
    error,
    fetchMyAppointments,
    fetchDashboardStats,
    clearError
  } = useAppointments();

  const [dashboardData, setDashboardData] = useState(representativeData);
  const [todayAppointments, setTodayAppointments] = useState([]);
  const [dashboardStats, setDashboardStats] = useState(null);

  // Fetch data on component mount
  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      // Fetch today's appointments
      const today = new Date().toISOString().split('T')[0];
      const appointmentsResponse = await fetchMyAppointments(1, 10, { date: today });

      if (appointmentsResponse?.data) {
        setTodayAppointments(appointmentsResponse.data);
      }

      // Fetch dashboard statistics
      const statsResponse = await fetchDashboardStats();
      if (statsResponse) {
        setDashboardStats(statsResponse);
        console.log('Dashboard stats loaded:', statsResponse);
      }
    } catch (err) {
      console.error('Error loading dashboard data:', err);
      Alert.alert('Erreur', 'Impossible de charger les données du tableau de bord');
    }
  };

  // Show error alert if there's an API error
  useEffect(() => {
    if (error) {
      Alert.alert('Erreur', error, [
        { text: 'OK', onPress: clearError }
      ]);
    }
  }, [error]);
  const StatCard = ({ title, value, subtitle, color = 'blue', icon, isLoading = false }) => (
    <View className="bg-white p-4 rounded-xl shadow-sm border border-gray-100 flex-1 mx-1">
      <View className="flex-row items-center justify-between mb-2">
        <IconSymbol name={icon} size={20} color={`#${color === 'blue' ? '3b82f6' : color === 'green' ? '10b981' : color === 'purple' ? '8b5cf6' : 'f59e0b'}`} />
      </View>
      <Text className="text-gray-600 text-sm font-medium">{title}</Text>
      {isLoading ? (
        <View className="flex-row items-center mt-1">
          <ActivityIndicator size="small" color={`#${color === 'blue' ? '3b82f6' : color === 'green' ? '10b981' : color === 'purple' ? '8b5cf6' : 'f59e0b'}`} />
          <Text className="text-gray-400 text-sm ml-2">Loading...</Text>
        </View>
      ) : (
        <Text className={`text-2xl font-bold mt-1 text-${color}-600`}>{value}</Text>
      )}
      {subtitle && !isLoading && <Text className="text-gray-500 text-xs mt-1">{subtitle}</Text>}
    </View>
  );

  const AppointmentCard = ({ client, time, assistant, type, estimatedValue, address }) => (
    <View className="bg-white p-4 rounded-xl shadow-sm border border-gray-100 mb-3">
      <View className="flex-row items-center justify-between mb-3">
        <View className="flex-1">
          <Text className="text-gray-900 font-semibold text-base">{client}</Text>
          <Text className="text-gray-500 text-sm">Scheduled by {assistant}</Text>
        </View>
        <View className="items-end">
          <Text className="text-blue-600 font-bold text-lg">{time}</Text>
          <View className={`px-2 py-1 rounded-full ${
            type === 'announced' ? 'bg-green-100' : 'bg-yellow-100'
          }`}>
            <Text className={`text-xs font-medium ${
              type === 'announced' ? 'text-green-700' : 'text-yellow-700'
            }`}>
              {type === 'announced' ? 'Announced' : 'Not Announced'}
            </Text>
          </View>
        </View>
      </View>
      <View className="border-t border-gray-100 pt-3">
        <View className="flex-row items-center mb-2">
          <IconSymbol name="location" size={14} color="#6b7280" />
          <Text className="text-gray-600 text-sm ml-2 flex-1">{address}</Text>
        </View>
        <View className="flex-row items-center">
          <IconSymbol name="eurosign.circle" size={14} color="#6b7280" />
          <Text className="text-gray-600 text-sm ml-2">Est. Value: €{estimatedValue}</Text>
        </View>
      </View>
    </View>
  );

  const SaleCard = ({ client, amount, profit, time, items }) => (
    <View className="bg-white p-4 rounded-xl shadow-sm border border-gray-100 mb-3">
      <View className="flex-row items-center justify-between mb-2">
        <View className="flex-1">
          <Text className="text-gray-900 font-semibold">{client}</Text>
          <Text className="text-gray-500 text-sm">{time} • {items}</Text>
        </View>
        <View className="items-end">
          <Text className="text-green-600 font-bold text-lg">€{amount}</Text>
          <Text className="text-gray-600 text-sm">Profit: €{profit}</Text>
        </View>
      </View>
    </View>
  );

  const QuickActionCard = ({ title, icon, onPress }) => (
    <TouchableOpacity
      className="bg-white p-4 rounded-xl shadow-sm border border-gray-100 items-center justify-center h-24 flex-1 mx-1"
      onPress={onPress}
    >
      <IconSymbol name={icon} size={24} color="#3b82f6" />
      <Text className="text-gray-700 text-sm font-medium mt-2 text-center">{title}</Text>
    </TouchableOpacity>
  );
  
  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <StatusBar barStyle="dark-content" backgroundColor="#f9fafb" />
      
      <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View className="bg-white px-4 py-6 border-b border-gray-100">
          <View className="flex-row items-center justify-between mb-6">
            <View className="flex-1">
              <Text className="text-2xl font-bold text-gray-900">
                Welcome, {user.name}
              </Text>
              <Text className="text-gray-600 mt-1">{user.role}</Text>
            </View>
            <View className="flex-row items-center">
              <TouchableOpacity
                className="w-10 h-10 bg-gray-100 rounded-full items-center justify-center mr-3"
                onPress={loadDashboardData}
                disabled={isLoading}
              >
                <IconSymbol
                  name="arrow.clockwise"
                  size={18}
                  color={isLoading ? "#9ca3af" : "#374151"}
                />
              </TouchableOpacity>
              <TouchableOpacity
                className="w-12 h-12 bg-blue-100 rounded-full items-center justify-center"
                onPress={() => router.push('/profile')}
              >
                <Text className="text-xl">{representativeData.user.avatar}</Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Today's Stats */}
          <View className="flex-row mb-4">
            <StatCard
              title="Scheduled Today"
              value={dashboardStats?.todays_appointments}
              subtitle="Appointments"
              color="blue"
              icon="calendar"
              isLoading={isLoading && !dashboardStats}
            />
            <StatCard
              title="Realized Today"
              value={dashboardStats?.todays_realized_appointments}
              subtitle="Completed"
              color="green"
              icon="checkmark.circle"
              isLoading={isLoading && !dashboardStats}
            />
          </View>
          <View className="flex-row">
            <StatCard
              title="Revenue Today"
              value={dashboardStats ? `€${dashboardStats.todays_revenue.toFixed(2)}` : `NAN`}
              subtitle="From sales"
              color="purple"
              icon="dollarsign.circle"
              isLoading={isLoading && !dashboardStats}
            />
            <StatCard
              title="Benefit Today"
              value={dashboardStats ? `€${dashboardStats.todays_benefit.toFixed(2)}` : `NAN`}
              subtitle="Profit earned"
              color="green"
              icon="chart.line.uptrend.xyaxis"
              isLoading={isLoading && !dashboardStats}
            />
          </View>
          <View className="flex-row mt-4">
            <StatCard
              title="Realization Rate"
              value={dashboardStats ? `${dashboardStats.realization_rate.toFixed(1)}%` : `NAN`}
              subtitle="Success rate"
              color="orange"
              icon="percent"
              isLoading={isLoading && !dashboardStats}
            />
            <View className="flex-1 mx-1">
              <View className="bg-white p-4 rounded-xl shadow-sm border border-gray-100">
                <View className="flex-row items-center justify-between mb-2">
                  <IconSymbol name="calendar.badge.clock" size={20} color="#6b7280" />
                </View>
                <Text className="text-gray-600 text-sm font-medium">Date</Text>
                <Text className="text-lg font-bold mt-1 text-gray-700">
                  {dashboardStats?.date ? new Date(dashboardStats.date).toLocaleDateString('fr-FR') : new Date().toLocaleDateString('fr-FR')}
                </Text>
                <Text className="text-gray-500 text-xs mt-1">Today's data</Text>
              </View>
            </View>
          </View>
        </View>

        {/* Quick Actions */}
        <View className="px-4 py-6">
          <Text className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</Text>
          <View className="flex-row">
            {representativeData.quickActions.map((action) => (
              <QuickActionCard
                key={action.id}
                title={action.title}
                icon={action.icon}
                onPress={() => router.push(action.route)}
              />
            ))}
          </View>
        </View>

        {/* Upcoming Appointments */}
        <View className="px-4 pb-6">
          <Text className="text-lg font-semibold text-gray-900 mb-4">Upcoming Appointments</Text>
          {representativeData.upcomingAppointments.map((appointment) => (
            <AppointmentCard
              key={appointment.id}
              client={appointment.client}
              time={appointment.time}
              assistant={appointment.assistant}
              type={appointment.type}
              estimatedValue={appointment.estimatedValue}
              address={appointment.address}
            />
          ))}
        </View>

        {/* Recent Sales */}
        <View className="px-4 pb-6">
          <Text className="text-lg font-semibold text-gray-900 mb-4">Recent Sales</Text>
          {representativeData.recentSales.map((sale) => (
            <SaleCard
              key={sale.id}
              client={sale.client}
              amount={sale.amount}
              profit={sale.profit}
              time={sale.time}
              items={sale.items}
            />
          ))}
        </View>

        {/* Weekly Performance Summary */}
        <View className="px-4 pb-6">
          <Text className="text-lg font-semibold text-gray-900 mb-4">Weekly Performance</Text>
          <View className="bg-white p-4 rounded-xl shadow-sm border border-gray-100">
            <View className="flex-row justify-between items-center mb-3">
              <Text className="text-gray-600">Appointments Completed</Text>
              <Text className="text-gray-900 font-semibold">{representativeData.weeklyPerformance.appointmentsCompleted}</Text>
            </View>
            <View className="flex-row justify-between items-center mb-3">
              <Text className="text-gray-600">Sales Made</Text>
              <Text className="text-green-600 font-semibold">{representativeData.weeklyPerformance.salesMade}</Text>
            </View>
            <View className="flex-row justify-between items-center mb-3">
              <Text className="text-gray-600">Total Revenue</Text>
              <Text className="text-blue-600 font-semibold">€{representativeData.weeklyPerformance.totalRevenue.toLocaleString()}</Text>
            </View>
            <View className="flex-row justify-between items-center">
              <Text className="text-gray-600">Average Sale Value</Text>
              <Text className="text-purple-600 font-semibold">€{representativeData.weeklyPerformance.avgSaleValue}</Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
