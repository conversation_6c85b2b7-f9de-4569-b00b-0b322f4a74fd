import axios from "axios";

// AS-Shop API Configuration
// const APP_URL = process.env.EXPO_PUBLIC_APP_URL;
const APP_URL = "http://192.168.11.105:8000/api/v1"; // Update this with your actual API URL

const IMAGE_URL = `${APP_URL}/storage/`
const VIDEO_URL = `${APP_URL}/storage/`

// Generic HTTP methods without Token requirement
const get = async (endpoint) => {
    try {
        const response = await axios.get(`${APP_URL}/api/${endpoint}`);
        return response;
    } catch (error) {
        console.log(`API ERROR\nMethod: GET\nEndpoint: ${endpoint}\nError: ${error}`);
        return null;
    }
};

const post = async (endpoint, data) => {
    try {
        const response = await axios.post(`${APP_URL}/api/${endpoint}`, data);
        return response;
    } catch (error) {
        console.log(`API ERROR\nMethod: POST\nEndpoint: ${endpoint}\nError: ${error}`);
        return null;
    }
};

const put = async (endpoint, data) => {
    try {
        const response = await axios.put(`${APP_URL}/api/${endpoint}`, data);
        return response;
    } catch (error) {
        console.log(`API ERROR\nMethod: PUT\nEndpoint: ${endpoint}\nError: ${error}`);
        return null;
    }
};

const remove = async (endpoint) => {
    try {
        const response = await axios.delete(`${APP_URL}/api/${endpoint}`);
        return response;
    } catch (error) {
        console.log(`API ERROR\nMethod: DELETE\nEndpoint: ${endpoint}\nError: ${error}`);
        return null;
    }
};

// AS-Shop Specific API Functions

// Authentication
export const login = async (email, password) => {
    console.log(email, password);
    try {
        const response = await axios.post(`${APP_URL}/auth/login`, { email, password });
        return response;
    } catch (error) {
        console.log('Login API Error:', error);
        return null;
    }
};

// Appointments
export const createAppointment = async (appointmentData) => {
    try {
        const response = await post('appointments', appointmentData);
        return response;
    } catch (error) {
        console.log('Create Appointment API Error:', error);
        return null;
    }
};

export const getAppointments = async () => {
    try {
        const response = await get('appointments');
        return response;
    } catch (error) {
        console.log('Get Appointments API Error:', error);
        return null;
    }
};

export const getAppointmentById = async (id) => {
    try {
        const response = await get(`appointments/${id}`);
        return response;
    } catch (error) {
        console.log('Get Appointment API Error:', error);
        return null;
    }
};

export const updateAppointment = async (id, appointmentData) => {
    try {
        const response = await put(`appointments/${id}`, appointmentData);
        return response;
    } catch (error) {
        console.log('Update Appointment API Error:', error);
        return null;
    }
};

// Users
export const getUsers = async () => {
    try {
        const response = await get('users');
        return response;
    } catch (error) {
        console.log('Get Users API Error:', error);
        return null;
    }
};

export const getUsersByRole = async (role) => {
    try {
        const response = await get(`users/role/${role}`);
        return response;
    } catch (error) {
        console.log('Get Users by Role API Error:', error);
        return null;
    }
};

export default {
    get,
    put,
    post,
    remove,
    APP_URL,
    IMAGE_URL,
    VIDEO_URL,
    // AS-Shop specific functions
    login,
    createAppointment,
    getAppointments,
    getAppointmentById,
    updateAppointment,
    getUsers,
    getUsersByRole
};