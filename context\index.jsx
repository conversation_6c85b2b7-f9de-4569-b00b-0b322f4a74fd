import {
  createContext,
  useContext,
  useEffect,
  useState,
} from "react";
import api from "@/api";

const appContext = createContext();

const AppProvider = ({ children }) => {
  //* get participamts from the backend
  const [participants, setParticipants] = useState([]);
  const fetchParticipants = async () => {
    try {
      const response = await api.get("participants/?auth=" + user?.id);
      setParticipants(response.data.participants);
    } catch (error) {
      console.error("❌ Failed to fetch participants:", error);
    } finally {
      //
    }
  };

  const appValue = {
    participants,
  };
  useEffect(() => {
    fetchParticipants();
  }, []);
  return (
    <appContext.Provider value={appValue}>
      {children}
    </appContext.Provider>
  );
};

const useAppContext = () => useContext(appContext);

export { AppProvider, appContext, useAppContext };
