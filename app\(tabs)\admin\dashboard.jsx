import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
} from 'react-native';
import { router } from 'expo-router';
import { IconSymbol } from '@/components/ui/IconSymbol';

// Mock data for Administrator
const adminData = {
  user: {
    name: '<PERSON>',
    role: 'Administrator',
    avatar: '⚙️',
  },
  systemOverview: {
    totalUsers: 18,
    activeUsers: 16,
    totalAppointments: 1247,
    systemUptime: 99.8,
  },
  userStats: {
    assistants: { total: 8, active: 8, performance: 85 },
    representatives: { total: 5, active: 5, performance: 78 },
    recruiters: { total: 2, active: 2, performance: 92 },
    executives: { total: 2, active: 1, performance: 95 },
    admins: { total: 1, active: 1, performance: 100 },
  },
  recentActivity: [
    { id: 1, action: 'User created', user: '<PERSON> (Assistant)', time: '2 hours ago', type: 'user' },
    { id: 2, action: 'Pairing updated', details: '<PERSON> ↔ <PERSON>', time: '4 hours ago', type: 'pairing' },
    { id: 3, action: 'Data export', details: 'Appointments database (Excel)', time: '6 hours ago', type: 'export' },
    { id: 4, action: 'System backup', details: 'Automated daily backup completed', time: '1 day ago', type: 'system' },
  ],
  quickActions: [
    { id: 1, title: 'User Management', icon: 'person.3', route: '/admin/users' },
    { id: 2, title: 'Pairing System', icon: 'person.2.circle', route: '/admin/pairings' },
    { id: 3, title: 'Database', icon: 'server.rack', route: '/admin/appointments' },
    { id: 4, title: 'Leaderboard', icon: 'trophy', route: '/admin/podium' },
    { id: 5, title: 'Analytics', icon: 'chart.bar.doc.horizontal', route: '/admin/analytics' },
    { id: 6, title: 'System Settings', icon: 'gear', route: '/admin/settings' },
  ],
  systemHealth: {
    database: { status: 'healthy', uptime: 99.9, lastBackup: '2 hours ago' },
    api: { status: 'healthy', responseTime: 120, requests: 15420 },
    storage: { status: 'warning', usage: 78, capacity: '2.5TB' },
  },
  alerts: [
    { id: 1, type: 'warning', message: 'Storage usage at 78% capacity', priority: 'medium' },
    { id: 2, type: 'info', message: 'Scheduled maintenance in 3 days', priority: 'low' },
    { id: 3, type: 'success', message: 'All user accounts verified successfully', priority: 'low' },
  ],
};

export default function AdminDashboardScreen() {
  const [selectedView, setSelectedView] = useState('overview');

  const MetricCard = ({ title, value, subtitle, color = 'blue', icon, status }) => (
    <View className="bg-white p-4 rounded-xl shadow-sm border border-gray-100 flex-1 mx-1 mb-3">
      <View className="flex-row items-center justify-between mb-2">
        <IconSymbol name={icon} size={20} color={`#${color === 'blue' ? '3b82f6' : color === 'green' ? '10b981' : color === 'purple' ? '8b5cf6' : color === 'orange' ? 'f59e0b' : 'dc2626'}`} />
        {status && (
          <View className={`w-3 h-3 rounded-full ${
            status === 'healthy' ? 'bg-green-500' : status === 'warning' ? 'bg-yellow-500' : 'bg-red-500'
          }`} />
        )}
      </View>
      <Text className="text-gray-600 text-sm font-medium">{title}</Text>
      <Text className={`text-xl font-bold mt-1 text-${color}-600`}>{value}</Text>
      {subtitle && <Text className="text-gray-500 text-xs mt-1">{subtitle}</Text>}
    </View>
  );

  const QuickActionCard = ({ title, icon, onPress }) => (
    <TouchableOpacity
      className="bg-white p-3 rounded-xl shadow-sm border border-gray-100 items-center justify-center h-20 flex-1 mx-1 mb-2"
      onPress={onPress}
    >
      <IconSymbol name={icon} size={18} color="#3b82f6" />
      <Text className="text-gray-700 text-xs font-medium mt-1 text-center">{title}</Text>
    </TouchableOpacity>
  );

  const UserRoleCard = ({ role, data }) => (
    <View className="bg-white p-4 rounded-xl shadow-sm border border-gray-100 mb-3">
      <View className="flex-row items-center justify-between mb-3">
        <Text className="text-gray-900 font-semibold capitalize">{role}</Text>
        <View className={`px-2 py-1 rounded-full ${
          data.performance >= 90 ? 'bg-green-100' : data.performance >= 80 ? 'bg-yellow-100' : 'bg-red-100'
        }`}>
          <Text className={`text-xs font-medium ${
            data.performance >= 90 ? 'text-green-700' : data.performance >= 80 ? 'text-yellow-700' : 'text-red-700'
          }`}>
            {data.performance}%
          </Text>
        </View>
      </View>
      <View className="flex-row justify-between">
        <View>
          <Text className="text-gray-500 text-sm">Total Users</Text>
          <Text className="text-gray-900 font-semibold">{data.total}</Text>
        </View>
        <View>
          <Text className="text-gray-500 text-sm">Active</Text>
          <Text className="text-green-600 font-semibold">{data.active}</Text>
        </View>
        <View>
          <Text className="text-gray-500 text-sm">Performance</Text>
          <Text className="text-blue-600 font-semibold">{data.performance}%</Text>
        </View>
      </View>
    </View>
  );

  const ActivityItem = ({ action, user, details, time, type }) => (
    <View className="flex-row items-center py-3 border-b border-gray-100">
      <View className={`w-8 h-8 rounded-full items-center justify-center mr-3 ${
        type === 'user' ? 'bg-blue-100' : 
        type === 'pairing' ? 'bg-purple-100' : 
        type === 'export' ? 'bg-green-100' : 'bg-gray-100'
      }`}>
        <IconSymbol 
          name={type === 'user' ? 'person.badge.plus' : type === 'pairing' ? 'person.2.circle' : type === 'export' ? 'square.and.arrow.up' : 'gear'} 
          size={16} 
          color={type === 'user' ? '#3b82f6' : type === 'pairing' ? '#8b5cf6' : type === 'export' ? '#10b981' : '#6b7280'} 
        />
      </View>
      <View className="flex-1">
        <Text className="text-gray-900 font-medium text-sm">{action}</Text>
        <Text className="text-gray-500 text-xs mt-1">{user || details}</Text>
      </View>
      <Text className="text-gray-400 text-xs">{time}</Text>
    </View>
  );

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <StatusBar barStyle="dark-content" backgroundColor="#f9fafb" />
      
      <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View className="bg-white px-4 py-6 border-b border-gray-100">
          <View className="flex-row items-center justify-between mb-6">
            <View>
              <Text className="text-2xl font-bold text-gray-900">
                Admin Dashboard
              </Text>
              <Text className="text-gray-600 mt-1">System Management & Analytics</Text>
            </View>
            <TouchableOpacity 
              className="w-12 h-12 bg-blue-100 rounded-full items-center justify-center"
              onPress={() => router.push('/profile')}
            >
              <Text className="text-xl">{adminData.user.avatar}</Text>
            </TouchableOpacity>
          </View>

          {/* System Overview */}
          <View className="flex-row mb-4">
            <MetricCard 
              title="Total Users" 
              value={adminData.systemOverview.totalUsers}
              subtitle="All roles"
              color="blue"
              icon="person.3"
            />
            <MetricCard 
              title="Active Users" 
              value={adminData.systemOverview.activeUsers}
              subtitle="Currently online"
              color="green"
              icon="person.circle"
              status="healthy"
            />
          </View>
          <View className="flex-row">
            <MetricCard 
              title="Total Appointments" 
              value={adminData.systemOverview.totalAppointments.toLocaleString()}
              subtitle="All time"
              color="purple"
              icon="calendar"
            />
            <MetricCard 
              title="System Uptime" 
              value={`${adminData.systemOverview.systemUptime}%`}
              subtitle="This month"
              color="orange"
              icon="server.rack"
              status="healthy"
            />
          </View>
        </View>

        {/* Quick Actions */}
        <View className="px-4 py-6">
          <Text className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</Text>
          <View className="flex-row flex-wrap">
            {adminData.quickActions.map((action) => (
              <View key={action.id} className="w-1/3 p-1">
                <QuickActionCard
                  title={action.title}
                  icon={action.icon}
                  onPress={() => router.push(action.route)}
                />
              </View>
            ))}
          </View>
        </View>

        {/* View Selector */}
        <View className="px-4 mb-4">
          <View className="flex-row space-x-2">
            <TouchableOpacity 
              className={`px-4 py-2 rounded-lg ${selectedView === 'overview' ? 'bg-blue-500' : 'bg-gray-100'}`}
              onPress={() => setSelectedView('overview')}
            >
              <Text className={`font-medium ${selectedView === 'overview' ? 'text-white' : 'text-gray-700'}`}>
                User Overview
              </Text>
            </TouchableOpacity>
            <TouchableOpacity 
              className={`px-4 py-2 rounded-lg ${selectedView === 'activity' ? 'bg-blue-500' : 'bg-gray-100'}`}
              onPress={() => setSelectedView('activity')}
            >
              <Text className={`font-medium ${selectedView === 'activity' ? 'text-white' : 'text-gray-700'}`}>
                Recent Activity
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Content based on selected view */}
        {selectedView === 'overview' && (
          <View className="px-4 pb-6">
            <Text className="text-lg font-semibold text-gray-900 mb-4">User Roles Overview</Text>
            {Object.entries(adminData.userStats).map(([role, data]) => (
              <UserRoleCard key={role} role={role} data={data} />
            ))}
          </View>
        )}

        {selectedView === 'activity' && (
          <View className="px-4 pb-6">
            <Text className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</Text>
            <View className="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
              {adminData.recentActivity.map((activity) => (
                <ActivityItem
                  key={activity.id}
                  action={activity.action}
                  user={activity.user}
                  details={activity.details}
                  time={activity.time}
                  type={activity.type}
                />
              ))}
            </View>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}
