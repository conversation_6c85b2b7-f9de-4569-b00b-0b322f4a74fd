import React, { useState } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  TextInput,
  SafeAreaView,
  StatusBar,
} from 'react-native';
import { router } from 'expo-router';
import { IconSymbol } from '@/components/ui/IconSymbol';

// Mock sales data
const mockSales = [
  {
    id: '1',
    clientName: '<PERSON>',
    appointmentId: 'APT-001',
    purchaseAmount: 250,
    profit: 75,
    conversionRate: 100,
    date: '2024-07-24',
    time: '09:00',
    status: 'completed',
    itemType: 'Gold Ring',
    weight: '15g',
  },
  {
    id: '2',
    clientName: '<PERSON>',
    appointmentId: 'APT-002',
    purchaseAmount: 180,
    profit: 45,
    conversionRate: 100,
    date: '2024-07-24',
    time: '14:00',
    status: 'completed',
    itemType: 'Silver Necklace',
    weight: '25g',
  },
  {
    id: '3',
    clientName: '<PERSON>',
    appointmentId: 'APT-003',
    purchaseAmount: 0,
    profit: 0,
    conversionRate: 0,
    date: '2024-07-23',
    time: '11:00',
    status: 'no_purchase',
    itemType: 'Consultation Only',
    weight: '0g',
  },
  {
    id: '4',
    clientName: '<PERSON>',
    appointmentId: 'APT-004',
    purchaseAmount: 420,
    profit: 120,
    conversionRate: 100,
    date: '2024-07-23',
    time: '16:00',
    status: 'completed',
    itemType: 'Diamond Earrings',
    weight: '8g',
  },
];

export default function SalesScreen() {
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('All');

  const filteredSales = mockSales.filter(sale =>
    sale.clientName.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const totalRevenue = mockSales.reduce((sum, sale) => sum + sale.purchaseAmount, 0);
  const totalProfit = mockSales.reduce((sum, sale) => sum + sale.profit, 0);
  const conversionRate = Math.round((mockSales.filter(s => s.status === 'completed').length / mockSales.length) * 100);

  const renderSaleItem = ({ item }) => (
    <TouchableOpacity
      className="bg-white mx-4 mb-3 p-4 rounded-xl shadow-sm border border-gray-100"
      onPress={() => router.push(`/sales/${item.id}`)}
    >
      <View className="flex-row items-center justify-between mb-3">
        <View className="flex-1">
          <Text className="text-gray-900 font-semibold text-base">{item.clientName}</Text>
          <Text className="text-gray-500 text-sm">{item.appointmentId} • {item.itemType}</Text>
        </View>
        <View className="items-end">
          <Text className={`text-lg font-bold ${item.purchaseAmount > 0 ? 'text-green-600' : 'text-gray-400'}`}>
            €{item.purchaseAmount}
          </Text>
          <View className={`px-2 py-1 rounded-full ${
            item.status === 'completed' ? 'bg-green-100' : 'bg-red-100'
          }`}>
            <Text className={`text-xs font-medium ${
              item.status === 'completed' ? 'text-green-700' : 'text-red-700'
            }`}>
              {item.status === 'completed' ? 'Sold' : 'No Sale'}
            </Text>
          </View>
        </View>
      </View>
      
      <View className="flex-row justify-between items-center pt-3 border-t border-gray-100">
        <View className="flex-row items-center">
          <IconSymbol name="calendar" size={14} color="#6b7280" />
          <Text className="text-gray-500 text-sm ml-1">{item.date} at {item.time}</Text>
        </View>
        <Text className="text-gray-600 text-sm">Profit: €{item.profit}</Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <StatusBar barStyle="dark-content" backgroundColor="#f9fafb" />
      
      {/* Header with Stats */}
      <View className="bg-white px-4 py-4 border-b border-gray-100">
        <View className="flex-row items-center justify-between mb-4">
          <Text className="text-2xl font-bold text-gray-900">Sales & Purchases</Text>
          <TouchableOpacity className="w-10 h-10 bg-green-500 rounded-full items-center justify-center">
            <IconSymbol name="plus" size={20} color="white" />
          </TouchableOpacity>
        </View>

        {/* Quick Stats */}
        <View className="flex-row mb-4 space-x-2">
          <View className="bg-green-50 p-3 rounded-lg flex-1">
            <Text className="text-green-600 text-sm font-medium">Total Revenue</Text>
            <Text className="text-green-700 text-lg font-bold">€{totalRevenue}</Text>
          </View>
          <View className="bg-blue-50 p-3 rounded-lg flex-1">
            <Text className="text-blue-600 text-sm font-medium">Total Profit</Text>
            <Text className="text-blue-700 text-lg font-bold">€{totalProfit}</Text>
          </View>
          <View className="bg-purple-50 p-3 rounded-lg flex-1">
            <Text className="text-purple-600 text-sm font-medium">Conversion</Text>
            <Text className="text-purple-700 text-lg font-bold">{conversionRate}%</Text>
          </View>
        </View>

        {/* Search Bar */}
        <View className="flex-row items-center bg-gray-100 rounded-lg px-3 py-2 mb-4">
          <IconSymbol name="magnifyingglass" size={16} color="#6b7280" />
          <TextInput
            className="flex-1 ml-2 text-gray-900"
            placeholder="Search sales..."
            placeholderTextColor="#6b7280"
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
        </View>

        {/* Filter Buttons */}
        <View className="flex-row space-x-3">
          <TouchableOpacity className="bg-gray-100 px-4 py-2 rounded-lg">
            <Text className="text-gray-700 font-medium">All Sales</Text>
          </TouchableOpacity>
          <TouchableOpacity className="bg-gray-100 px-4 py-2 rounded-lg">
            <Text className="text-gray-700 font-medium">This Week</Text>
          </TouchableOpacity>
          <TouchableOpacity className="bg-gray-100 px-4 py-2 rounded-lg">
            <Text className="text-gray-700 font-medium">Completed</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Sales List */}
      <FlatList
        data={filteredSales}
        renderItem={renderSaleItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={{ paddingTop: 16, paddingBottom: 100 }}
        showsVerticalScrollIndicator={false}
      />
    </SafeAreaView>
  );
}
