import React, { useState } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  TextInput,
  SafeAreaView,
  StatusBar,
} from 'react-native';
import { router } from 'expo-router';
import { IconSymbol } from '@/components/ui/IconSymbol';

// Mock data for appointments
const mockAppointments = [
  {
    id: '1',
    clientName: 'Meeting with <PERSON>',
    time: '9:00 AM - 10:00 AM',
    date: 'July 24, 2024',
    status: 'active',
    avatar: '👩‍💼',
  },
  {
    id: '2',
    clientName: 'Client Consultation',
    time: '2:00 PM - 3:00 PM',
    date: 'July 24, 2024',
    status: 'active',
    avatar: '👨‍💼',
  },
  {
    id: '3',
    clientName: 'Team Sync',
    time: '4:00 PM - 5:00 PM',
    date: 'July 24, 2024',
    status: 'active',
    avatar: '👥',
  },
  {
    id: '4',
    clientName: 'Follow-up Call with <PERSON>',
    time: '10:00 AM - 11:00 AM',
    date: 'July 25, 2024',
    status: 'active',
    avatar: '📞',
  },
  {
    id: '5',
    clientName: 'Product Demo',
    time: '1:00 PM - 2:00 PM',
    date: 'July 25, 2024',
    status: 'active',
    avatar: '💻',
  },
  {
    id: '6',
    clientName: 'Review Session',
    time: '3:00 PM - 4:00 PM',
    date: 'July 25, 2024',
    status: 'active',
    avatar: '📋',
  },
];

export default function AppointmentsScreen() {
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('All');
  const [typeFilter, setTypeFilter] = useState('Type');

  const filteredAppointments = mockAppointments.filter(appointment =>
    appointment.clientName.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const renderAppointmentItem = ({ item }) => (
    <TouchableOpacity
      className="bg-white mx-4 mb-3 p-4 rounded-xl shadow-sm border border-gray-100"
      onPress={() => router.push(`/appointements/${item.id}`)}
    >
      <View className="flex-row items-center justify-between">
        <View className="flex-row items-center flex-1">
          <View className="w-10 h-10 bg-gray-100 rounded-full items-center justify-center mr-3">
            <Text className="text-lg">{item.avatar}</Text>
          </View>
          <View className="flex-1">
            <Text className="text-gray-900 font-medium text-base">{item.clientName}</Text>
            <Text className="text-gray-500 text-sm mt-1">{item.time}</Text>
          </View>
        </View>
        <View className="items-end">
          <View className="w-3 h-3 bg-green-500 rounded-full mb-1"></View>
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <StatusBar barStyle="dark-content" backgroundColor="#f9fafb" />

      {/* Header */}
      <View className="bg-white px-4 py-4 border-b border-gray-100">
        <View className="flex-row items-center justify-between mb-4">
          <Text className="text-2xl font-bold text-gray-900">Appointments</Text>
          <TouchableOpacity className="w-10 h-10 bg-blue-500 rounded-full items-center justify-center">
            <IconSymbol name="plus" size={20} color="white" />
          </TouchableOpacity>
        </View>

        {/* Search Bar */}
        <View className="flex-row items-center bg-gray-100 rounded-lg px-3 py-2 mb-4">
          <IconSymbol name="magnifyingglass" size={16} color="#6b7280" />
          <TextInput
            className="flex-1 ml-2 text-gray-900"
            placeholder="Search"
            placeholderTextColor="#6b7280"
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
        </View>

        {/* Filter Buttons */}
        <View className="flex-row space-x-3">
          <TouchableOpacity className="bg-gray-100 px-4 py-2 rounded-lg">
            <Text className="text-gray-700 font-medium">Status ▼</Text>
          </TouchableOpacity>
          <TouchableOpacity className="bg-gray-100 px-4 py-2 rounded-lg">
            <Text className="text-gray-700 font-medium">Date ▼</Text>
          </TouchableOpacity>
          <TouchableOpacity className="bg-gray-100 px-4 py-2 rounded-lg">
            <Text className="text-gray-700 font-medium">Type ▼</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Appointments List */}
      <FlatList
        data={filteredAppointments}
        renderItem={renderAppointmentItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={{ paddingTop: 16, paddingBottom: 100 }}
        showsVerticalScrollIndicator={false}
      />
    </SafeAreaView>
  );
}