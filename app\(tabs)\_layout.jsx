import { Tabs } from "expo-router";
import React from "react";
import { Platform } from "react-native";

import { HapticTab } from "@/components/HapticTab";
import { IconSymbol } from "@/components/ui/IconSymbol";
import TabBarBackground from "@/components/ui/TabBarBackground";
import { Colors } from "@/constants/Colors";
import { useUser } from "@/contexts/UserContext";

export default function TabLayout() {
  const { user } = useUser();
  const userRole = user?.role || "assistant";

  // all screens config
  const roleRoutes = {
    assistant: [
      {
        name: "salesAssistant/dashboard",
        title: "Dashboard",
        icon: "house.fill",
      },
      {
        name: "salesAssistant/appointments",
        title: "Appointments",
        icon: "calendar",
      },
      {
        name: "salesAssistant/performance",
        title: "Performance",
        icon: "chart.bar",
      },
      {
        name: "salesAssistant/profile",
        title: "Profile",
        icon: "person",
      },
    ],
    representative: [
      {
        name: "representative/dashboard",
        title: "Dashboard",
        icon: "house.fill",
      },
      {
        name: "representative/appointments",
        title: "Appointments",
        icon: "calendar",
      },
      {
        name: "representative/performance",
        title: "Performance",
        icon: "chart.bar",
      },
      {
        name: "representative/profile",
        title: "Profile",
        icon: "person",
      },
    ],
    recruiter: [
      { name: "recruiter/dashboard", title: "Dashboard", icon: "house.fill" },
      {
        name: "recruiter/appointments",
        title: "Appointments",
        icon: "calendar",
      },
      {
        name: "recruiter/performance",
        title: "Performance",
        icon: "chart.bar",
      },
      { name: "recruiter/profile", title: "Profile", icon: "person" },
    ],
    executive: [
      { name: "executive/dashboard", title: "Dashboard", icon: "house.fill" },
      {
        name: "executive/performance",
        title: "Performance",
        icon: "chart.bar",
      },
      { name: "executive/profile", title: "Profile", icon: "person" },
    ],
    admin: [
      { name: "admin/dashboard", title: "Dashboard", icon: "house.fill" },
      { name: "admin/performance", title: "Performance", icon: "chart.bar" },
      { name: "admin/profile", title: "Profile", icon: "person.circle" },
    ],
  };

  const visibleScreens = roleRoutes[userRole] || roleRoutes.assistant;

  // flatten all available screens
  const allRoleScreens = Object.values(roleRoutes).flat();

  // hide every screen that is not in visibleScreens
  const hiddenRoleScreens = allRoleScreens.filter(
    (screen) => !visibleScreens.some((s) => s.name === screen.name)
  );

  // your custom hidden pages
  const customHiddenScreens = [
    { route: "appointmentCreation", title: "Create Appointment" },
    { route: "appointmentDetails", title: "Appointment Details" },
    { route: "settings", title: "Settings" },
    { route: "index", title: "Index" },
    { route: "explore", title: "Explore" },
    { route: "appointements/index", title: "Appointments List" },
    { route: "appointements/[id]", title: "Appointment Details" },
    { route: "sales/index", title: "Sales List" },
    { route: "sales/[id]", title: "Sale Details" },
    { route: "representative/post-rdv", title: "Post-RDV Form" },
    { route: "representative/post-rdv-view", title: "Post-RDV View" },
    { route: "profile", title: "Post-RDV View" },
    { route: "performance", title: "Post-RDV View" },
    { route: "dashboard", title: "Post-RDV View" },
  ];

  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: Colors.light.tint,
        headerShown: false,
        tabBarButton: HapticTab,
        tabBarBackground: TabBarBackground,
        tabBarStyle: Platform.select({
          ios: { position: "absolute" },
          default: {},
        }),
      }}
    >
      {/* Visible tabs only for current role */}
      {visibleScreens.map((screen) => (
        <Tabs.Screen
          key={screen.name}
          name={screen.name}
          options={{
            title: screen.title,
            tabBarIcon: ({ color }) => (
              <IconSymbol size={28} name={screen.icon} color={color} />
            ),
          }}
        />
      ))}

      {/* Automatically hide all screens that don't belong to user role */}
      {hiddenRoleScreens.map((screen) => (
        <Tabs.Screen
          key={screen.name}
          name={screen.name}
          options={{
            href: null,
            // tabBarButton: () => null,
          }}
        />
      ))}

      {/* custom hidden pages */}
      {customHiddenScreens.map((screen) => (
        <Tabs.Screen
          key={screen.route}
          name={screen.route}
          options={{
            href: null,
            // tabBarButton: () => null,
          }}
        />
      ))}
    </Tabs>
  );
}
