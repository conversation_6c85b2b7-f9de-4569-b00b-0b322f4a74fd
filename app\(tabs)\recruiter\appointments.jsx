import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  TextInput,
} from 'react-native';
import { router } from 'expo-router';
import { IconSymbol } from '@/components/ui/IconSymbol';

// Mock appointments data for <PERSON><PERSON><PERSON><PERSON> (with pairing analysis focus)
const appointmentsData = [
  {
    id: 1,
    client: '<PERSON>',
    phone: '+33 1 23 45 67 89',
    address: '15 Rue de la Paix, 75001 Paris',
    time: '09:00',
    date: '2024-03-15',
    assistant: '<PERSON>',
    representative: '<PERSON>',
    status: 'confirmed',
    source: 'leboncoin',
    type: 'announced',
    estimatedValue: 200,
    pairingScore: 92,
    notes: 'High-performing pairing'
  },
  {
    id: 2,
    client: '<PERSON>',
    phone: '+33 1 98 76 54 32',
    address: '8 Avenue des Champs, 69001 Lyon',
    time: '11:30',
    date: '2024-03-15',
    assistant: '<PERSON>',
    representative: '<PERSON>',
    status: 'pending',
    source: 'outbound_call',
    type: 'not_announced',
    estimatedValue: 350,
    pairingScore: 88,
    notes: 'Good collaboration potential'
  },
  {
    id: 3,
    client: '<PERSON>',
    phone: '+33 1 11 22 33 44',
    address: '22 Boulevard Saint-Germain, 75005 Paris',
    time: '14:00',
    date: '2024-03-15',
    assistant: 'Lisa Chen',
    representative: 'Thomas Blanc',
    status: 'confirmed',
    source: 'leboncoin',
    type: 'announced',
    estimatedValue: 150,
    pairingScore: 75,
    notes: 'Needs optimization'
  },
];

export default function RecruiterAppointmentsScreen() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('all');

  const filteredAppointments = appointmentsData.filter(appointment => {
    const matchesSearch = appointment.client.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         appointment.assistant.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         appointment.representative.toLowerCase().includes(searchQuery.toLowerCase());
    
    if (selectedFilter === 'all') return matchesSearch;
    if (selectedFilter === 'high_performance') return matchesSearch && appointment.pairingScore >= 90;
    if (selectedFilter === 'needs_optimization') return matchesSearch && appointment.pairingScore < 80;
    
    return matchesSearch;
  });

  const AppointmentCard = ({ appointment }) => (
    <TouchableOpacity
      className="bg-white p-4 rounded-xl shadow-sm border border-gray-100 mb-3"
      onPress={() => router.push(`/appointements/${appointment.id}`)}
    >
      <View className="flex-row items-center justify-between mb-3">
        <View className="flex-1">
          <Text className="text-gray-900 font-semibold text-base">{appointment.client}</Text>
          <Text className="text-gray-500 text-sm">{appointment.time} • {appointment.assistant} → {appointment.representative}</Text>
        </View>
        <View className="items-end">
          <View className={`px-3 py-1 rounded-full ${
            appointment.status === 'confirmed' ? 'bg-green-100' : 'bg-yellow-100'
          }`}>
            <Text className={`text-xs font-medium ${
              appointment.status === 'confirmed' ? 'text-green-700' : 'text-yellow-700'
            }`}>
              {appointment.status === 'confirmed' ? 'Confirmed' : 'Pending'}
            </Text>
          </View>
          <View className={`px-2 py-1 rounded-full mt-1 ${
            appointment.pairingScore >= 90 ? 'bg-green-100' : 
            appointment.pairingScore >= 80 ? 'bg-yellow-100' : 'bg-red-100'
          }`}>
            <Text className={`text-xs font-medium ${
              appointment.pairingScore >= 90 ? 'text-green-700' : 
              appointment.pairingScore >= 80 ? 'text-yellow-700' : 'text-red-700'
            }`}>
              {appointment.pairingScore}% Match
            </Text>
          </View>
        </View>
      </View>
      
      <View className="border-t border-gray-100 pt-3">
        <View className="flex-row items-center mb-2">
          <IconSymbol name="location" size={14} color="#6b7280" />
          <Text className="text-gray-600 text-sm ml-2 flex-1">{appointment.address}</Text>
        </View>
        <View className="flex-row items-center justify-between mb-2">
          <View className="flex-row items-center">
            <IconSymbol name="person.2.circle" size={14} color="#6b7280" />
            <Text className="text-gray-600 text-sm ml-2">Pairing Score: {appointment.pairingScore}%</Text>
          </View>
          <View className="flex-row items-center">
            <IconSymbol name="eurosign.circle" size={14} color="#6b7280" />
            <Text className="text-gray-600 text-sm ml-2">Est. €{appointment.estimatedValue}</Text>
          </View>
        </View>
        {appointment.notes && (
          <View className="flex-row items-center">
            <IconSymbol name="note.text" size={14} color="#6b7280" />
            <Text className="text-gray-600 text-sm ml-2 flex-1">{appointment.notes}</Text>
          </View>
        )}
      </View>
    </TouchableOpacity>
  );

  const FilterButton = ({ title, value, count }) => (
    <TouchableOpacity
      className={`px-4 py-2 rounded-lg mr-2 ${
        selectedFilter === value ? 'bg-blue-500' : 'bg-gray-100'
      }`}
      onPress={() => setSelectedFilter(value)}
    >
      <Text className={`font-medium ${
        selectedFilter === value ? 'text-white' : 'text-gray-700'
      }`}>
        {title} {count !== undefined && `(${count})`}
      </Text>
    </TouchableOpacity>
  );

  const highPerformanceCount = appointmentsData.filter(a => a.pairingScore >= 90).length;
  const needsOptimizationCount = appointmentsData.filter(a => a.pairingScore < 80).length;

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <StatusBar barStyle="dark-content" backgroundColor="#f9fafb" />
      
      <View className="flex-1">
        {/* Header */}
        <View className="bg-white px-4 py-6 border-b border-gray-100">
          <View className="flex-row items-center justify-between mb-4">
            <Text className="text-2xl font-bold text-gray-900">Team Appointments</Text>
            <TouchableOpacity 
              className="bg-blue-500 px-4 py-2 rounded-lg"
              onPress={() => router.push('/recruiter/pairings')}
            >
              <Text className="text-white font-medium">Optimize</Text>
            </TouchableOpacity>
          </View>

          {/* Search Bar */}
          <View className="bg-gray-50 border border-gray-200 rounded-lg px-4 py-3 mb-4">
            <View className="flex-row items-center">
              <IconSymbol name="magnifyingglass" size={20} color="#6b7280" />
              <TextInput
                className="flex-1 ml-3 text-gray-900"
                placeholder="Search by client, assistant, or representative..."
                placeholderTextColor="#9ca3af"
                value={searchQuery}
                onChangeText={setSearchQuery}
              />
            </View>
          </View>

          {/* Filter Buttons */}
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <View className="flex-row">
              <FilterButton title="All Pairings" value="all" count={appointmentsData.length} />
              <FilterButton title="High Performance" value="high_performance" count={highPerformanceCount} />
              <FilterButton title="Needs Optimization" value="needs_optimization" count={needsOptimizationCount} />
            </View>
          </ScrollView>
        </View>

        {/* Appointments List */}
        <ScrollView className="flex-1 px-4 py-4" showsVerticalScrollIndicator={false}>
          {filteredAppointments.length > 0 ? (
            filteredAppointments.map((appointment) => (
              <AppointmentCard key={appointment.id} appointment={appointment} />
            ))
          ) : (
            <View className="flex-1 items-center justify-center py-12">
              <IconSymbol name="person.2.circle" size={64} color="#d1d5db" />
              <Text className="text-gray-500 text-lg font-medium mt-4">No appointments found</Text>
              <Text className="text-gray-400 text-center mt-2">
                {searchQuery ? 'Try adjusting your search terms' : 'No team appointments match your criteria'}
              </Text>
            </View>
          )}
        </ScrollView>
      </View>
    </SafeAreaView>
  );
}
