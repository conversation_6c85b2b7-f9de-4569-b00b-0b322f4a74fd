import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
} from 'react-native';
import { IconSymbol } from '@/components/ui/IconSymbol';

export default function SalesAssistantPerformanceScreen() {
  const [selectedPeriod, setSelectedPeriod] = useState('weekly');

  const performanceData = {
    weeklyStats: {
      appointmentsCreated: 24,
      appointmentsAnnounced: 18,
      validationRate: 75,
      bonusEarned: 180,
    },
    monthlyStats: {
      appointmentsCreated: 96,
      appointmentsAnnounced: 72,
      validationRate: 78,
      bonusEarned: 720,
    },
    leaderboard: [
      { name: '<PERSON>', appointments: 24, position: 1, isCurrentUser: true },
      { name: '<PERSON>', appointments: 22, position: 2, isCurrentUser: false },
      { name: '<PERSON>', appointments: 19, position: 3, isCurrentUser: false },
    ],
  };

  const currentStats = selectedPeriod === 'weekly' ? performanceData.weeklyStats : performanceData.monthlyStats;

  const MetricCard = ({ title, value, subtitle, color = 'blue', icon, trend }) => (
    <View className="bg-white p-4 rounded-xl shadow-sm border border-gray-100 flex-1 mx-1 mb-3">
      <View className="flex-row items-center justify-between mb-2">
        <IconSymbol name={icon} size={20} color={`#${color === 'blue' ? '3b82f6' : color === 'green' ? '10b981' : color === 'purple' ? '8b5cf6' : 'f59e0b'}`} />
        {trend && (
          <View className={`px-2 py-1 rounded-full ${trend > 0 ? 'bg-green-100' : 'bg-red-100'}`}>
            <Text className={`text-xs font-medium ${trend > 0 ? 'text-green-700' : 'text-red-700'}`}>
              {trend > 0 ? '+' : ''}{trend}%
            </Text>
          </View>
        )}
      </View>
      <Text className="text-gray-600 text-sm font-medium">{title}</Text>
      <Text className={`text-xl font-bold mt-1 text-${color}-600`}>{value}</Text>
      {subtitle && <Text className="text-gray-500 text-xs mt-1">{subtitle}</Text>}
    </View>
  );

  const LeaderboardItem = ({ name, appointments, position, isCurrentUser }) => (
    <View className={`flex-row items-center justify-between py-3 px-4 rounded-lg mb-2 ${
      isCurrentUser ? 'bg-blue-50 border border-blue-200' : 'bg-gray-50'
    }`}>
      <View className="flex-row items-center">
        <View className={`w-8 h-8 rounded-full items-center justify-center mr-3 ${
          position === 1 ? 'bg-yellow-100' : position === 2 ? 'bg-gray-100' : 'bg-orange-100'
        }`}>
          <Text className={`font-bold ${
            position === 1 ? 'text-yellow-600' : position === 2 ? 'text-gray-600' : 'text-orange-600'
          }`}>
            {position}
          </Text>
        </View>
        <Text className={`font-medium ${isCurrentUser ? 'text-blue-900' : 'text-gray-900'}`}>
          {name}
        </Text>
      </View>
      <Text className={`font-semibold ${isCurrentUser ? 'text-blue-600' : 'text-gray-600'}`}>
        {appointments}
      </Text>
    </View>
  );

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <StatusBar barStyle="dark-content" backgroundColor="#f9fafb" />
      
      <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View className="bg-white px-4 py-6 border-b border-gray-100">
          <Text className="text-2xl font-bold text-gray-900 mb-2">
            My Performance
          </Text>
          <Text className="text-gray-600">Track your appointment creation success</Text>
        </View>

        {/* Period Selector */}
        <View className="px-4 py-4">
          <View className="flex-row space-x-2">
            <TouchableOpacity 
              className={`px-4 py-2 rounded-lg ${selectedPeriod === 'weekly' ? 'bg-blue-500' : 'bg-gray-100'}`}
              onPress={() => setSelectedPeriod('weekly')}
            >
              <Text className={`font-medium ${selectedPeriod === 'weekly' ? 'text-white' : 'text-gray-700'}`}>
                Weekly
              </Text>
            </TouchableOpacity>
            <TouchableOpacity 
              className={`px-4 py-2 rounded-lg ${selectedPeriod === 'monthly' ? 'bg-blue-500' : 'bg-gray-100'}`}
              onPress={() => setSelectedPeriod('monthly')}
            >
              <Text className={`font-medium ${selectedPeriod === 'monthly' ? 'text-white' : 'text-gray-700'}`}>
                Monthly
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Performance Metrics */}
        <View className="px-4 pb-6">
          <Text className="text-lg font-semibold text-gray-900 mb-4">Performance Metrics</Text>
          <View className="flex-row mb-4">
            <MetricCard 
              title="Appointments Created" 
              value={currentStats.appointmentsCreated}
              subtitle={`This ${selectedPeriod.replace('ly', '')}`}
              color="blue"
              icon="calendar.badge.plus"
              trend={5}
            />
            <MetricCard 
              title="Announced" 
              value={currentStats.appointmentsAnnounced}
              subtitle="Confirmed"
              color="green"
              icon="checkmark.circle"
              trend={8}
            />
          </View>
          <View className="flex-row">
            <MetricCard 
              title="Validation Rate" 
              value={`${currentStats.validationRate}%`}
              subtitle="Success rate"
              color="purple"
              icon="chart.line.uptrend.xyaxis"
              trend={3}
            />
            <MetricCard 
              title="Bonus Earned" 
              value={`€${currentStats.bonusEarned}`}
              subtitle={`This ${selectedPeriod.replace('ly', '')}`}
              color="orange"
              icon="eurosign.circle"
              trend={12}
            />
          </View>
        </View>

        {/* Leaderboard */}
        <View className="px-4 pb-6">
          <Text className="text-lg font-semibold text-gray-900 mb-4">Team Leaderboard</Text>
          <View>
            {performanceData.leaderboard.map((item, index) => (
              <LeaderboardItem
                key={index}
                name={item.name}
                appointments={item.appointments}
                position={item.position}
                isCurrentUser={item.isCurrentUser}
              />
            ))}
          </View>
        </View>

        {/* Performance Tips */}
        <View className="px-4 pb-6">
          <Text className="text-lg font-semibold text-gray-900 mb-4">Performance Tips</Text>
          <View className="bg-blue-50 border border-blue-200 rounded-xl p-4">
            <View className="flex-row items-center mb-2">
              <IconSymbol name="lightbulb" size={20} color="#3b82f6" />
              <Text className="text-blue-900 font-semibold ml-2">Tip of the Week</Text>
            </View>
            <Text className="text-blue-800 text-sm">
              Focus on announced appointments - they have a 25% higher conversion rate than unannounced ones.
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
