import { IconSymbol } from '@/components/ui/IconSymbol';
import { useUser } from '@/contexts/UserContext';
import { router } from 'expo-router';
import React from 'react';
import {
    SafeAreaView,
    ScrollView,
    StatusBar,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';

// Mock data for Sales Assistant
const assistantData = {
  user: {
    name: '<PERSON>',
    role: 'Sales Assistant',
    avatar: '📞',
  },
  weeklyStats: {
    appointmentsCreated: 24,
    appointmentsAnnounced: 18,
    validationRate: 75,
    bonusEarned: 180,
  },
  todayAppointments: [
    { id: 1, client: '<PERSON>', time: '09:00', representative: '<PERSON>', status: 'confirmed' },
    { id: 2, client: '<PERSON>', time: '11:30', representative: '<PERSON>', status: 'pending' },
    { id: 3, client: '<PERSON>', time: '14:00', representative: '<PERSON>', status: 'confirmed' },
  ],
  quickActions: [
    { id: 1, title: 'Create Appointment', icon: 'calendar.badge.plus', route: '/appointmentCreation' },
    { id: 2, title: 'View History', icon: 'clock.arrow.circlepath', route: '/appointmentsHistory' },
    { id: 3, title: 'Performance', icon: 'chart.bar', route: '/assistantPerformance' },
  ],
  leaderboard: [
    { name: '<PERSON>', appointments: 24, position: 1, isCurrentUser: true },
    { name: 'Marc Dubois', appointments: 22, position: 2, isCurrentUser: false },
    { name: 'Lisa Chen', appointments: 19, position: 3, isCurrentUser: false },
  ],
};

export default function SalesAssistantDashboardScreen() {
  const { user } = useUser();
  const StatCard = ({ title, value, subtitle, color = 'blue', icon }) => (
    <View className="bg-white p-4 rounded-xl shadow-sm border border-gray-100 flex-1 mx-1">
      <View className="flex-row items-center justify-between mb-2">
        <IconSymbol name={icon} size={20} color={`#${color === 'blue' ? '3b82f6' : color === 'green' ? '10b981' : color === 'purple' ? '8b5cf6' : 'f59e0b'}`} />
      </View>
      <Text className="text-gray-600 text-sm font-medium">{title}</Text>
      <Text className={`text-2xl font-bold mt-1 text-${color}-600`}>{value}</Text>
      {subtitle && <Text className="text-gray-500 text-xs mt-1">{subtitle}</Text>}
    </View>
  );

  const QuickActionCard = ({ title, icon, onPress }) => (
    <TouchableOpacity
      className="bg-white p-4 rounded-xl shadow-sm border border-gray-100 items-center justify-center h-24 flex-1 mx-1"
      onPress={onPress}
    >
      <IconSymbol name={icon} size={24} color="#3b82f6" />
      <Text className="text-gray-700 text-sm font-medium mt-2 text-center">{title}</Text>
    </TouchableOpacity>
  );

  const AppointmentItem = ({ client, time, representative, status }) => (
    <View className="flex-row items-center justify-between py-3 border-b border-gray-100">
      <View className="flex-1">
        <Text className="text-gray-900 font-medium">{client}</Text>
        <Text className="text-gray-500 text-sm">{time} • {representative}</Text>
      </View>
      <View className={`px-3 py-1 rounded-full ${
        status === 'confirmed' ? 'bg-green-100' : 'bg-yellow-100'
      }`}>
        <Text className={`text-xs font-medium ${
          status === 'confirmed' ? 'text-green-700' : 'text-yellow-700'
        }`}>
          {status === 'confirmed' ? 'Confirmed' : 'Pending'}
        </Text>
      </View>
    </View>
  );

  const LeaderboardItem = ({ name, appointments, position, isCurrentUser }) => (
    <View className={`flex-row items-center justify-between py-3 px-4 rounded-lg mb-2 ${
      isCurrentUser ? 'bg-blue-50 border border-blue-200' : 'bg-gray-50'
    }`}>
      <View className="flex-row items-center">
        <View className={`w-8 h-8 rounded-full items-center justify-center mr-3 ${
          position === 1 ? 'bg-yellow-100' : position === 2 ? 'bg-gray-100' : 'bg-orange-100'
        }`}>
          <Text className={`font-bold ${
            position === 1 ? 'text-yellow-600' : position === 2 ? 'text-gray-600' : 'text-orange-600'
          }`}>
            {position}
          </Text>
        </View>
        <Text className={`font-medium ${isCurrentUser ? 'text-blue-900' : 'text-gray-900'}`}>
          {name}
        </Text>
      </View>
      <Text className={`font-semibold ${isCurrentUser ? 'text-blue-600' : 'text-gray-600'}`}>
        {appointments}
      </Text>
    </View>
  );

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <StatusBar barStyle="dark-content" backgroundColor="#f9fafb" />
      
      <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View className="bg-white px-4 py-6 border-b border-gray-100">
          <View className="flex-row items-center justify-between mb-6">
            <View>
              <Text className="text-2xl font-bold text-gray-900">
                Welcome, {user?.name || assistantData.user.name}
              </Text>
              <Text className="text-gray-600 mt-1">{user?.role || assistantData.user.role}</Text>
            </View>
            <TouchableOpacity 
              className="w-12 h-12 bg-blue-100 rounded-full items-center justify-center"
              onPress={() => router.push('/profile')}
            >
              <Text className="text-xl">{assistantData.user.avatar}</Text>
            </TouchableOpacity>
          </View>

          {/* Weekly Performance Stats */}
          <View className="flex-row mb-4">
            <StatCard 
              title="Appointments Created" 
              value={assistantData.weeklyStats.appointmentsCreated}
              subtitle="This week"
              color="blue"
              icon="calendar.badge.plus"
            />
            <StatCard 
              title="Announced" 
              value={assistantData.weeklyStats.appointmentsAnnounced}
              subtitle="Confirmed"
              color="green"
              icon="checkmark.circle"
            />
          </View>
          <View className="flex-row">
            <StatCard 
              title="Validation Rate" 
              value={`${assistantData.weeklyStats.validationRate}%`}
              subtitle="Success rate"
              color="purple"
              icon="chart.line.uptrend.xyaxis"
            />
            <StatCard 
              title="Bonus Earned" 
              value={`€${assistantData.weeklyStats.bonusEarned}`}
              subtitle="This week"
              color="orange"
              icon="eurosign.circle"
            />
          </View>
        </View>

        {/* Quick Actions */}
        <View className="px-4 py-6">
          <Text className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</Text>
          <View className="flex-row">
            {assistantData.quickActions.map((action) => (
              <QuickActionCard
                key={action.id}
                title={action.title}
                icon={action.icon}
                onPress={() => router.push(action.route)}
              />
            ))}
          </View>
        </View>

        {/* Today's Appointments */}
        <View className="px-4 pb-6">
          <Text className="text-lg font-semibold text-gray-900 mb-4">Today's Appointments</Text>
          <View className="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
            {assistantData.todayAppointments.map((appointment) => (
              <AppointmentItem
                key={appointment.id}
                client={appointment.client}
                time={appointment.time}
                representative={appointment.representative}
                status={appointment.status}
              />
            ))}
          </View>
        </View>

        {/* Weekly Leaderboard */}
        <View className="px-4 pb-6">
          <Text className="text-lg font-semibold text-gray-900 mb-4">Weekly Leaderboard</Text>
          <View>
            {assistantData.leaderboard.map((item, index) => (
              <LeaderboardItem
                key={index}
                name={item.name}
                appointments={item.appointments}
                position={item.position}
                isCurrentUser={item.isCurrentUser}
              />
            ))}
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
