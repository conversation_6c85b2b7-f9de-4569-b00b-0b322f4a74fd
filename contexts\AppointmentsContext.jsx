import axios from 'axios';
import React, { createContext, useContext, useState } from 'react';
import { useUser } from './UserContext';

const AppointmentsContext = createContext();

export const useAppointments = () => {
  const context = useContext(AppointmentsContext);
  if (!context) {
    throw new Error('useAppointments must be used within an AppointmentsProvider');
  }
  return context;
};

const APP_URL = "http://192.168.159.140:8000/api/v1";

export const AppointmentsProvider = ({ children }) => {
  const { authToken } = useUser();
  const [appointments, setAppointments] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    perPage: 15,
  });

  // Create axios instance with auth headers
  const createAuthenticatedRequest = () => {
    return axios.create({
      baseURL: APP_URL,
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json',
      },
    });
  };

  // Fetch my appointments with pagination
  const fetchMyAppointments = async (page = 1, perPage = 15, filters = {}) => {
    if (!authToken) {
      setError('No authentication token available');
      return null;
    }

    setIsLoading(true);
    setError(null);

    try {
      const api = createAuthenticatedRequest();
      const params = {
        page,
        per_page: perPage,
        ...filters,
      };

      const response = await api.get(APP_URL+'/appointments/my-appointments', { params });
      console.log('appointments : ', response.data.data);
      if (response.data) {
        const { data, meta } = response.data;
        
        setAppointments(data || []);
        setPagination({
          currentPage: meta?.current_page || page,
          totalPages: meta?.last_page || 1,
          totalItems: meta?.total || 0,
          perPage: meta?.per_page || perPage,
        });

        return response.data;
      }
      
      return null;
    } catch (error) {
      console.error('Error fetching appointments:', error);
      setError(error.response?.data?.message || 'Failed to fetch appointments');
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch appointments for a specific date
  const fetchAppointmentsByDate = async (date, page = 1, perPage = 15) => {
    const filters = { date };
    return await fetchMyAppointments(page, perPage, filters);
  };

  // Fetch appointment details by ID
  const fetchAppointmentById = async (appointmentId) => {
    if (!authToken) {
      setError('No authentication token available');
      return null;
    }

    setIsLoading(true);
    setError(null);

    try {
      const api = createAuthenticatedRequest();
      const response = await api.get(`/appointments/${appointmentId}`);
      
      if (response.data) {
        return response.data;
      }
      
      return null;
    } catch (error) {
      console.error('Error fetching appointment details:', error);
      setError(error.response?.data?.message || 'Failed to fetch appointment details');
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  // Create a new appointment
  const createAppointment = async (appointmentData) => {
    if (!authToken) {
      setError('No authentication token available');
      return null;
    }

    setIsLoading(true);
    setError(null);

    try {
      const api = createAuthenticatedRequest();
      const response = await api.post('/appointments', appointmentData);
      
      if (response.data) {
        // Refresh appointments list after creation
        await fetchMyAppointments();
        return response.data;
      }
      
      return null;
    } catch (error) {
      console.error('Error creating appointment:', error);
      setError(error.response?.data?.message || 'Failed to create appointment');
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  // Update an appointment
  const updateAppointment = async (appointmentId, appointmentData) => {
    if (!authToken) {
      setError('No authentication token available');
      return null;
    }

    setIsLoading(true);
    setError(null);

    try {
      const api = createAuthenticatedRequest();
      const response = await api.put(`/appointments/${appointmentId}`, appointmentData);
      
      if (response.data) {
        // Update local appointments list
        setAppointments(prev => 
          prev.map(apt => apt.id === appointmentId ? { ...apt, ...response.data } : apt)
        );
        return response.data;
      }
      
      return null;
    } catch (error) {
      console.error('Error updating appointment:', error);
      setError(error.response?.data?.message || 'Failed to update appointment');
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  // Submit Post-RDV report
  const submitPostRDV = async (appointmentId, postRDVData) => {
    if (!authToken) {
      setError('No authentication token available');
      return null;
    }

    setIsLoading(true);
    setError(null);

    try {
      const api = createAuthenticatedRequest();
      const response = await api.post(`/appointments/${appointmentId}/post-rdv`, postRDVData);
      
      if (response.data) {
        // Update appointment status to completed
        setAppointments(prev => 
          prev.map(apt => 
            apt.id === appointmentId 
              ? { ...apt, status: 'completed', post_rdv: response.data } 
              : apt
          )
        );
        return response.data;
      }
      
      return null;
    } catch (error) {
      console.error('Error submitting Post-RDV:', error);
      setError(error.response?.data?.message || 'Failed to submit Post-RDV');
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  // Get Post-RDV details
  const fetchPostRDV = async (appointmentId) => {
    if (!authToken) {
      setError('No authentication token available');
      return null;
    }

    setIsLoading(true);
    setError(null);

    try {
      const api = createAuthenticatedRequest();
      const response = await api.get(`/appointments/${appointmentId}/post-rdv`);

      if (response.data) {
        return response.data;
      }

      return null;
    } catch (error) {
      console.error('Error fetching Post-RDV:', error);
      setError(error.response?.data?.message || 'Failed to fetch Post-RDV');
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  // Store purchase records for individual items
  const storePurchase = async (purchaseData) => {
    if (!authToken) {
      setError('No authentication token available');
      return null;
    }

    setIsLoading(true);
    setError(null);

    try {
      const api = createAuthenticatedRequest();
      const response = await api.post('/purchases', purchaseData);

      if (response.data) {
        return response.data;
      }

      return null;
    } catch (error) {
      console.error('Error storing purchase:', error);
      setError(error.response?.data?.message || 'Failed to store purchase');
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  // Get representative dashboard statistics
  const fetchDashboardStats = async () => {
    if (!authToken) {
      setError('No authentication token available');
      return null;
    }

    setIsLoading(true);
    setError(null);

    try {
      const api = createAuthenticatedRequest();
      const response = await api.get('/appointments/representative-dashboard-stats');

      if (response.data && response.data.success) {
        console.log('Dashboard stats:', response.data.data);
        return response.data.data; // Return the data object containing the stats
      }

      // Handle case where success is false
      if (response.data && !response.data.success) {
        setError(response.data.message || 'Failed to fetch dashboard statistics');
        return null;
      }

      return null;
    } catch (error) {
      console.error('Error fetching representative dashboard stats:', error);
      setError(error.response?.data?.message || 'Failed to fetch dashboard statistics');
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  // Clear error
  const clearError = () => {
    setError(null);
  };

  // Clear appointments
  const clearAppointments = () => {
    setAppointments([]);
    setPagination({
      currentPage: 1,
      totalPages: 1,
      totalItems: 0,
      perPage: 15,
    });
  };

  const value = {
    // State
    appointments,
    isLoading,
    error,
    pagination,
    
    // Actions
    fetchMyAppointments,
    fetchAppointmentsByDate,
    fetchAppointmentById,
    createAppointment,
    updateAppointment,
    submitPostRDV,
    fetchPostRDV,
    storePurchase,
    fetchDashboardStats,
    clearError,
    clearAppointments,
  };

  return (
    <AppointmentsContext.Provider value={value}>
      {children}
    </AppointmentsContext.Provider>
  );
};
