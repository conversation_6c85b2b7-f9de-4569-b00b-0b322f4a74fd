import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  TextInput,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { router } from 'expo-router';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useUser } from '@/contexts/UserContext';
import API from '@/api/index';

export default function AppointmentCreationScreen() {
  const { user } = useUser();
  const [isLoading, setIsLoading] = useState(false);
  const [representatives, setRepresentatives] = useState([]);
  
  // Form state
  const [formData, setFormData] = useState({
    assistant_id: user?.id || '',
    representative_id: '',
    client_name: '',
    client_phone: '',
    client_address: '',
    source: 'outbound',
    date_time: '',
    notes: '',
    items_collection: [],
    appointment_type: 'announced',
  });

  const [newItem, setNewItem] = useState('');

  useEffect(() => {
    loadRepresentatives();
  }, []);

  const loadRepresentatives = async () => {
    try {
      const response = await API.getUsersByRole('representative');
      if (response && response.data) {
        setRepresentatives(response.data);
      }
    } catch (error) {
      console.error('Error loading representatives:', error);
      // Mock data for development
      setRepresentatives([
        { id: 1, name: 'Alex Martin' },
        { id: 2, name: 'Sophie Chen' },
        { id: 3, name: 'Thomas Blanc' },
      ]);
    }
  };

  const updateFormData = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const addItem = () => {
    if (newItem.trim()) {
      setFormData(prev => ({
        ...prev,
        items_collection: [...prev.items_collection, newItem.trim()]
      }));
      setNewItem('');
    }
  };

  const removeItem = (index) => {
    setFormData(prev => ({
      ...prev,
      items_collection: prev.items_collection.filter((_, i) => i !== index)
    }));
  };

  const handleSubmit = async () => {
    // Validation
    if (!formData.representative_id) {
      Alert.alert('Error', 'Please select a representative');
      return;
    }
    if (!formData.client_name.trim()) {
      Alert.alert('Error', 'Please enter client name');
      return;
    }
    if (!formData.client_phone.trim()) {
      Alert.alert('Error', 'Please enter client phone');
      return;
    }
    if (!formData.client_address.trim()) {
      Alert.alert('Error', 'Please enter client address');
      return;
    }
    if (!formData.date_time) {
      Alert.alert('Error', 'Please select date and time');
      return;
    }
    if (formData.items_collection.length === 0) {
      Alert.alert('Error', 'Please add at least one item to collect');
      return;
    }

    setIsLoading(true);

    try {
      const response = await API.createAppointment(formData);
      
      if (response && response.data) {
        Alert.alert(
          'Success',
          'Appointment created successfully!',
          [
            {
              text: 'OK',
              onPress: () => router.back()
            }
          ]
        );
      } else {
        Alert.alert('Error', 'Failed to create appointment');
      }
    } catch (error) {
      console.error('Error creating appointment:', error);
      Alert.alert('Error', 'Failed to create appointment. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const InputField = ({ label, value, onChangeText, placeholder, multiline = false }) => (
    <View className="mb-4">
      <Text className="text-gray-700 font-medium mb-2">{label}</Text>
      <TextInput
        className={`bg-gray-50 border border-gray-200 rounded-lg px-4 py-3 text-gray-900 ${multiline ? 'h-20' : ''}`}
        placeholder={placeholder}
        placeholderTextColor="#9ca3af"
        value={value}
        onChangeText={onChangeText}
        multiline={multiline}
        textAlignVertical={multiline ? 'top' : 'center'}
      />
    </View>
  );

  const SelectField = ({ label, value, onSelect, options, placeholder }) => (
    <View className="mb-4">
      <Text className="text-gray-700 font-medium mb-2">{label}</Text>
      <View className="bg-gray-50 border border-gray-200 rounded-lg">
        {options.map((option) => (
          <TouchableOpacity
            key={option.value}
            className={`p-3 border-b border-gray-100 ${value === option.value ? 'bg-blue-50' : ''}`}
            onPress={() => onSelect(option.value)}
          >
            <View className="flex-row items-center">
              <View className={`w-4 h-4 rounded-full border-2 mr-3 ${
                value === option.value ? 'bg-blue-500 border-blue-500' : 'border-gray-300'
              }`} />
              <Text className={`${value === option.value ? 'text-blue-700 font-medium' : 'text-gray-700'}`}>
                {option.label}
              </Text>
            </View>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <StatusBar barStyle="dark-content" backgroundColor="#f9fafb" />
      
      {/* Header */}
      <View className="bg-white px-4 py-4 border-b border-gray-100 flex-row items-center">
        <TouchableOpacity onPress={() => router.back()} className="mr-4">
          <IconSymbol name="chevron.left" size={24} color="#374151" />
        </TouchableOpacity>
        <Text className="text-xl font-bold text-gray-900">Create Appointment</Text>
      </View>

      <ScrollView className="flex-1 px-4 py-6" showsVerticalScrollIndicator={false}>
        {/* Representative Selection */}
        <View className="mb-4">
          <Text className="text-gray-700 font-medium mb-2">Representative</Text>
          <View className="bg-gray-50 border border-gray-200 rounded-lg">
            {representatives.map((rep) => (
              <TouchableOpacity
                key={rep.id}
                className={`p-3 border-b border-gray-100 ${formData.representative_id === rep.id ? 'bg-blue-50' : ''}`}
                onPress={() => updateFormData('representative_id', rep.id)}
              >
                <View className="flex-row items-center">
                  <View className={`w-4 h-4 rounded-full border-2 mr-3 ${
                    formData.representative_id === rep.id ? 'bg-blue-500 border-blue-500' : 'border-gray-300'
                  }`} />
                  <Text className={`${formData.representative_id === rep.id ? 'text-blue-700 font-medium' : 'text-gray-700'}`}>
                    {rep.name}
                  </Text>
                </View>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Client Information */}
        <InputField
          label="Client Name"
          value={formData.client_name}
          onChangeText={(value) => updateFormData('client_name', value)}
          placeholder="Enter client full name"
        />

        <InputField
          label="Client Phone"
          value={formData.client_phone}
          onChangeText={(value) => updateFormData('client_phone', value)}
          placeholder="+33 1 23 45 67 89"
        />

        <InputField
          label="Client Address"
          value={formData.client_address}
          onChangeText={(value) => updateFormData('client_address', value)}
          placeholder="Full address including city"
          multiline
        />

        {/* Source Selection */}
        <SelectField
          label="Appointment Source"
          value={formData.source}
          onSelect={(value) => updateFormData('source', value)}
          options={[
            { value: 'outbound', label: 'Outbound Call' },
            { value: 'leboncoin', label: 'Leboncoin' },
          ]}
        />

        {/* Appointment Type */}
        <SelectField
          label="Appointment Type"
          value={formData.appointment_type}
          onSelect={(value) => updateFormData('appointment_type', value)}
          options={[
            { value: 'announced', label: 'Announced' },
            { value: 'not_announced', label: 'Not Announced' },
          ]}
        />

        {/* Date and Time */}
        <InputField
          label="Date & Time"
          value={formData.date_time}
          onChangeText={(value) => updateFormData('date_time', value)}
          placeholder="YYYY-MM-DD HH:MM (e.g., 2024-03-15 14:30)"
        />

        {/* Items Collection */}
        <View className="mb-4">
          <Text className="text-gray-700 font-medium mb-2">Items to Collect</Text>
          <View className="flex-row mb-2">
            <TextInput
              className="flex-1 bg-gray-50 border border-gray-200 rounded-lg px-4 py-3 text-gray-900 mr-2"
              placeholder="Add item (e.g., Gold jewelry, Watches)"
              placeholderTextColor="#9ca3af"
              value={newItem}
              onChangeText={setNewItem}
            />
            <TouchableOpacity
              className="bg-blue-500 px-4 py-3 rounded-lg items-center justify-center"
              onPress={addItem}
            >
              <IconSymbol name="plus" size={20} color="white" />
            </TouchableOpacity>
          </View>
          
          {formData.items_collection.map((item, index) => (
            <View key={index} className="flex-row items-center bg-white p-3 rounded-lg mb-2 border border-gray-100">
              <Text className="flex-1 text-gray-900">{item}</Text>
              <TouchableOpacity onPress={() => removeItem(index)}>
                <IconSymbol name="trash" size={16} color="#ef4444" />
              </TouchableOpacity>
            </View>
          ))}
        </View>

        {/* Notes */}
        <InputField
          label="Notes (Optional)"
          value={formData.notes}
          onChangeText={(value) => updateFormData('notes', value)}
          placeholder="Additional notes about the appointment"
          multiline
        />

        {/* Submit Button */}
        <TouchableOpacity
          className={`bg-blue-500 rounded-lg py-4 items-center mt-6 ${isLoading ? 'opacity-50' : ''}`}
          onPress={handleSubmit}
          disabled={isLoading}
        >
          {isLoading ? (
            <ActivityIndicator color="white" />
          ) : (
            <Text className="text-white font-semibold text-lg">Create Appointment</Text>
          )}
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
}
