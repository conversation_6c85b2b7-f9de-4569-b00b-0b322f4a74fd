import React from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
} from 'react-native';
import { router } from 'expo-router';
import { IconSymbol } from '@/components/ui/IconSymbol';

// Mock dashboard data
const dashboardData = {
  user: {
    name: '<PERSON>',
    avatar: '👨‍💼',
  },
  todayStats: {
    appointments: 5,
    newAppointments: 15,
  },
  quickActions: [
    { id: 1, title: 'New Appointment', icon: 'calendar.badge.plus', route: '/appointements' },
    { id: 2, title: 'View History', icon: 'clock.arrow.circlepath', route: '/sales' },
  ],
  recentActivity: [
    { id: 1, title: 'Meeting with <PERSON> completed', time: '2 hours ago', type: 'appointment' },
    { id: 2, title: 'New purchase recorded - €250', time: '4 hours ago', type: 'sale' },
    { id: 3, title: 'Team performance updated', time: '6 hours ago', type: 'performance' },
  ],
};

export default function DashboardScreen() {
  const StatCard = ({ title, value, subtitle, color = 'blue' }) => (
    <View className="bg-white p-4 rounded-xl shadow-sm border border-gray-100 flex-1 mx-1">
      <Text className="text-gray-600 text-sm font-medium">{title}</Text>
      <Text className={`text-2xl font-bold mt-1 text-${color}-600`}>{value}</Text>
      {subtitle && <Text className="text-gray-500 text-xs mt-1">{subtitle}</Text>}
    </View>
  );

  const QuickActionCard = ({ title, icon, onPress }) => (
    <TouchableOpacity
      className="bg-white p-4 rounded-xl shadow-sm border border-gray-100 items-center justify-center h-24 flex-1 mx-1"
      onPress={onPress}
    >
      <IconSymbol name={icon} size={24} color="#3b82f6" />
      <Text className="text-gray-700 text-sm font-medium mt-2 text-center">{title}</Text>
    </TouchableOpacity>
  );

  const ActivityItem = ({ title, time, type }) => (
    <View className="flex-row items-center py-3 border-b border-gray-100">
      <View className={`w-8 h-8 rounded-full items-center justify-center mr-3 ${
        type === 'appointment' ? 'bg-blue-100' : 
        type === 'sale' ? 'bg-green-100' : 'bg-purple-100'
      }`}>
        <IconSymbol 
          name={type === 'appointment' ? 'calendar' : type === 'sale' ? 'dollarsign.circle' : 'chart.bar'} 
          size={16} 
          color={type === 'appointment' ? '#3b82f6' : type === 'sale' ? '#10b981' : '#8b5cf6'} 
        />
      </View>
      <View className="flex-1">
        <Text className="text-gray-900 font-medium text-sm">{title}</Text>
        <Text className="text-gray-500 text-xs mt-1">{time}</Text>
      </View>
    </View>
  );

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <StatusBar barStyle="dark-content" backgroundColor="#f9fafb" />
      
      <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View className="bg-white px-4 py-6 border-b border-gray-100">
          <View className="flex-row items-center justify-between mb-6">
            <View>
              <Text className="text-2xl font-bold text-gray-900">
                Welcome back, {dashboardData.user.name}
              </Text>
              <Text className="text-gray-600 mt-1">Here's what's happening today</Text>
            </View>
            <TouchableOpacity 
              className="w-12 h-12 bg-blue-100 rounded-full items-center justify-center"
              onPress={() => router.push('/profile')}
            >
              <Text className="text-xl">{dashboardData.user.avatar}</Text>
            </TouchableOpacity>
          </View>

          {/* Today's Stats */}
          <View className="flex-row mb-6">
            <StatCard 
              title="Today's Appointments" 
              value={dashboardData.todayStats.appointments}
              subtitle="2 completed"
              color="blue"
            />
            <StatCard 
              title="New Appointments" 
              value={dashboardData.todayStats.newAppointments}
              subtitle="This week"
              color="green"
            />
          </View>
        </View>

        {/* Quick Actions */}
        <View className="px-4 py-6">
          <Text className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</Text>
          <View className="flex-row">
            {dashboardData.quickActions.map((action) => (
              <QuickActionCard
                key={action.id}
                title={action.title}
                icon={action.icon}
                onPress={() => router.push(action.route)}
              />
            ))}
          </View>
        </View>

        {/* Recent Activity */}
        <View className="px-4 pb-6">
          <Text className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</Text>
          <View className="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
            {dashboardData.recentActivity.map((activity, index) => (
              <ActivityItem
                key={activity.id}
                title={activity.title}
                time={activity.time}
                type={activity.type}
              />
            ))}
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
