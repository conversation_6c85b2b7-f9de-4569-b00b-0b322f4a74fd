import { IconSymbol } from '@/components/ui/IconSymbol';
import { useUser } from '@/contexts/UserContext';
import React from 'react';
import {
    SafeAreaView,
    ScrollView,
    StatusBar,
    Switch,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';

// Mock user data for Admin
const userData = {
  name: '<PERSON>',
  role: 'Administrator',
  email: '<EMAIL>',
  phone: '+****************',
  avatar: '⚙️',
  stats: {
    totalUsers: 18,
    systemUptime: 99.8,
    dataProcessed: 15420,
    securityScore: 95,
  },
  settings: {
    notifications: true,
    autoSync: true,
    biometric: false,
  },
};

export default function AdminProfileScreen() {
  const { user, logout } = useUser();
  const StatCard = ({ title, value, subtitle, color = 'blue' }) => (
    <View className="bg-white p-4 rounded-xl shadow-sm border border-gray-100 flex-1 mx-1">
      <Text className="text-gray-600 text-sm font-medium">{title}</Text>
      <Text className={`text-xl font-bold mt-1 text-${color}-600`}>{value}</Text>
      {subtitle && <Text className="text-gray-500 text-xs mt-1">{subtitle}</Text>}
    </View>
  );

  const MenuSection = ({ title, children }) => (
    <View className="mb-6">
      <Text className="text-lg font-semibold text-gray-900 mb-3 px-4">{title}</Text>
      <View className="bg-white mx-4 rounded-xl shadow-sm border border-gray-100">
        {children}
      </View>
    </View>
  );

  const MenuItem = ({ icon, title, subtitle, onPress, rightElement, showBorder = true }) => (
    <TouchableOpacity 
      className={`flex-row items-center p-4 ${showBorder ? 'border-b border-gray-100' : ''}`}
      onPress={onPress}
    >
      <View className="w-10 h-10 bg-gray-100 rounded-full items-center justify-center mr-4">
        <IconSymbol name={icon} size={20} color="#6b7280" />
      </View>
      <View className="flex-1">
        <Text className="text-gray-900 font-medium">{title}</Text>
        {subtitle && <Text className="text-gray-500 text-sm mt-1">{subtitle}</Text>}
      </View>
      {rightElement || <IconSymbol name="chevron.right" size={16} color="#9ca3af" />}
    </TouchableOpacity>
  );

  const SettingToggle = ({ icon, title, subtitle, value, onValueChange }) => (
    <View className="flex-row items-center p-4 border-b border-gray-100">
      <View className="w-10 h-10 bg-gray-100 rounded-full items-center justify-center mr-4">
        <IconSymbol name={icon} size={20} color="#6b7280" />
      </View>
      <View className="flex-1">
        <Text className="text-gray-900 font-medium">{title}</Text>
        {subtitle && <Text className="text-gray-500 text-sm mt-1">{subtitle}</Text>}
      </View>
      <Switch
        value={value}
        onValueChange={onValueChange}
        trackColor={{ false: '#f3f4f6', true: '#3b82f6' }}
        thumbColor={value ? '#ffffff' : '#ffffff'}
      />
    </View>
  );

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <StatusBar barStyle="dark-content" backgroundColor="#f9fafb" />
      
      <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
        {/* Profile Header */}
        <View className="bg-white px-4 py-6 border-b border-gray-100">
          <View className="flex-row items-center mb-6">
            <View className="w-20 h-20 bg-blue-100 rounded-full items-center justify-center mr-4">
              <Text className="text-3xl">{userData.avatar}</Text>
            </View>
            <View className="flex-1">
              <Text className="text-2xl font-bold text-gray-900">{user?.name || userData.name}</Text>
              <Text className="text-gray-600 text-base">{user?.role || userData.role}</Text>
              <Text className="text-gray-500 text-sm mt-1">{user?.email || userData.email}</Text>
            </View>
            <TouchableOpacity className="w-10 h-10 bg-gray-100 rounded-full items-center justify-center">
              <IconSymbol name="pencil" size={16} color="#6b7280" />
            </TouchableOpacity>
          </View>

          {/* Quick Stats */}
          <View className="flex-row mb-4">
            <StatCard 
              title="Total Users" 
              value={userData.stats.totalUsers}
              subtitle="System wide"
              color="green"
            />
            <StatCard 
              title="System Uptime" 
              value={`${userData.stats.systemUptime}%`}
              subtitle="This month"
              color="blue"
            />
          </View>
          <View className="flex-row">
            <StatCard 
              title="Data Processed" 
              value={userData.stats.dataProcessed.toLocaleString()}
              subtitle="Today"
              color="purple"
            />
            <StatCard 
              title="Security Score" 
              value={`${userData.stats.securityScore}%`}
              subtitle="System security"
              color="orange"
            />
          </View>
        </View>

        {/* Account Section */}
        <MenuSection title="Account">
          <MenuItem 
            icon="person.circle" 
            title="Personal Information" 
            subtitle="Update your details"
            onPress={() => {}}
          />
          <MenuItem 
            icon="key" 
            title="Password & Security" 
            subtitle="Change password, 2FA"
            onPress={() => {}}
          />
          <MenuItem 
            icon="shield" 
            title="Admin Privileges" 
            subtitle="Manage system access"
            onPress={() => {}}
            showBorder={false}
          />
        </MenuSection>

        {/* Settings Section */}
        <MenuSection title="Settings">
          <SettingToggle
            icon="bell"
            title="System Notifications"
            subtitle="Critical alerts and updates"
            value={userData.settings.notifications}
            onValueChange={() => {}}
          />
          <SettingToggle
            icon="arrow.clockwise"
            title="Auto Sync"
            subtitle="Automatically sync data"
            value={userData.settings.autoSync}
            onValueChange={() => {}}
          />
          <MenuItem 
            icon="globe" 
            title="System Language" 
            subtitle="English"
            onPress={() => {}}
            showBorder={false}
          />
        </MenuSection>

        {/* Admin Tools */}
        <MenuSection title="Admin Tools">
          <MenuItem 
            icon="person.3" 
            title="User Management" 
            subtitle="Manage all user accounts"
            onPress={() => {}}
          />
          <MenuItem 
            icon="server.rack" 
            title="System Monitoring" 
            subtitle="Server health and performance"
            onPress={() => {}}
          />
          <MenuItem 
            icon="doc.text" 
            title="System Logs" 
            subtitle="View system activity logs"
            onPress={() => {}}
            showBorder={false}
          />
        </MenuSection>

        {/* Support Section */}
        <MenuSection title="Support">
          <MenuItem 
            icon="questionmark.circle" 
            title="Admin Documentation" 
            subtitle="System administration guide"
            onPress={() => {}}
          />
          <MenuItem 
            icon="envelope" 
            title="Technical Support" 
            subtitle="Contact system administrators"
            onPress={() => {}}
          />
          <MenuItem 
            icon="doc.text" 
            title="Terms & Privacy" 
            subtitle="Legal information"
            onPress={() => {}}
            showBorder={false}
          />
        </MenuSection>

        {/* Sign Out */}
        <View className="px-4 pb-6">
          <TouchableOpacity
            className="bg-red-50 border border-red-200 rounded-xl p-4 flex-row items-center justify-center"
            onPress={logout}
          >
            <IconSymbol name="arrow.right.square" size={20} color="#dc2626" />
            <Text className="text-red-600 font-semibold ml-2">Sign Out</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
