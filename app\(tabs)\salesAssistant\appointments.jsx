import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  TextInput,
} from 'react-native';
import { router } from 'expo-router';
import { IconSymbol } from '@/components/ui/IconSymbol';

// Mock appointments data for Sales Assistant
const appointmentsData = [
  {
    id: 1,
    client: '<PERSON>',
    phone: '+33 1 23 45 67 89',
    address: '15 Rue de la Paix, 75001 Paris',
    time: '09:00',
    date: '2024-03-15',
    representative: '<PERSON>',
    status: 'confirmed',
    source: 'leboncoin',
    type: 'announced',
    estimatedValue: 200,
    notes: 'Interested in gold jewelry'
  },
  {
    id: 2,
    client: '<PERSON>',
    phone: '+33 1 98 76 54 32',
    address: '8 Avenue des Champs, 69001 Lyon',
    time: '11:30',
    date: '2024-03-15',
    representative: '<PERSON>',
    status: 'pending',
    source: 'outbound_call',
    type: 'not_announced',
    estimatedValue: 350,
    notes: 'Has vintage watches to sell'
  },
  {
    id: 3,
    client: '<PERSON>',
    phone: '+33 1 11 22 33 44',
    address: '22 Boulevard Saint-Germain, 75005 Paris',
    time: '14:00',
    date: '2024-03-15',
    representative: '<PERSON>',
    status: 'confirmed',
    source: 'leboncoin',
    type: 'announced',
    estimatedValue: 150,
    notes: 'Silver items collection'
  },
];

export default function SalesAssistantAppointmentsScreen() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('all');

  const filteredAppointments = appointmentsData.filter(appointment => {
    const matchesSearch = appointment.client.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         appointment.address.toLowerCase().includes(searchQuery.toLowerCase());
    
    if (selectedFilter === 'all') return matchesSearch;
    if (selectedFilter === 'confirmed') return matchesSearch && appointment.status === 'confirmed';
    if (selectedFilter === 'pending') return matchesSearch && appointment.status === 'pending';
    
    return matchesSearch;
  });

  const AppointmentCard = ({ appointment }) => (
    <TouchableOpacity
      className="bg-white p-4 rounded-xl shadow-sm border border-gray-100 mb-3"
      onPress={() => router.push(`/appointements/${appointment.id}`)}
    >
      <View className="flex-row items-center justify-between mb-3">
        <View className="flex-1">
          <Text className="text-gray-900 font-semibold text-base">{appointment.client}</Text>
          <Text className="text-gray-500 text-sm">{appointment.time} • {appointment.representative}</Text>
        </View>
        <View className="items-end">
          <View className={`px-3 py-1 rounded-full ${
            appointment.status === 'confirmed' ? 'bg-green-100' : 'bg-yellow-100'
          }`}>
            <Text className={`text-xs font-medium ${
              appointment.status === 'confirmed' ? 'text-green-700' : 'text-yellow-700'
            }`}>
              {appointment.status === 'confirmed' ? 'Confirmed' : 'Pending'}
            </Text>
          </View>
          <View className={`px-2 py-1 rounded-full mt-1 ${
            appointment.type === 'announced' ? 'bg-blue-100' : 'bg-gray-100'
          }`}>
            <Text className={`text-xs font-medium ${
              appointment.type === 'announced' ? 'text-blue-700' : 'text-gray-700'
            }`}>
              {appointment.type === 'announced' ? 'Announced' : 'Not Announced'}
            </Text>
          </View>
        </View>
      </View>
      
      <View className="border-t border-gray-100 pt-3">
        <View className="flex-row items-center mb-2">
          <IconSymbol name="location" size={14} color="#6b7280" />
          <Text className="text-gray-600 text-sm ml-2 flex-1">{appointment.address}</Text>
        </View>
        <View className="flex-row items-center justify-between">
          <View className="flex-row items-center">
            <IconSymbol name="phone" size={14} color="#6b7280" />
            <Text className="text-gray-600 text-sm ml-2">{appointment.phone}</Text>
          </View>
          <View className="flex-row items-center">
            <IconSymbol name="eurosign.circle" size={14} color="#6b7280" />
            <Text className="text-gray-600 text-sm ml-2">Est. €{appointment.estimatedValue}</Text>
          </View>
        </View>
        {appointment.notes && (
          <View className="flex-row items-center mt-2">
            <IconSymbol name="note.text" size={14} color="#6b7280" />
            <Text className="text-gray-600 text-sm ml-2 flex-1">{appointment.notes}</Text>
          </View>
        )}
      </View>
    </TouchableOpacity>
  );

  const FilterButton = ({ title, value, count }) => (
    <TouchableOpacity
      className={`px-4 py-2 rounded-lg mr-2 ${
        selectedFilter === value ? 'bg-blue-500' : 'bg-gray-100'
      }`}
      onPress={() => setSelectedFilter(value)}
    >
      <Text className={`font-medium ${
        selectedFilter === value ? 'text-white' : 'text-gray-700'
      }`}>
        {title} {count !== undefined && `(${count})`}
      </Text>
    </TouchableOpacity>
  );

  const confirmedCount = appointmentsData.filter(a => a.status === 'confirmed').length;
  const pendingCount = appointmentsData.filter(a => a.status === 'pending').length;

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <StatusBar barStyle="dark-content" backgroundColor="#f9fafb" />
      
      <View className="flex-1">
        {/* Header */}
        <View className="bg-white px-4 py-6 border-b border-gray-100">
          <View className="flex-row items-center justify-between mb-4">
            <Text className="text-2xl font-bold text-gray-900">Appointments</Text>
            <TouchableOpacity 
              className="bg-blue-500 px-4 py-2 rounded-lg"
              onPress={() => router.push('/appointmentCreation')}
            >
              <Text className="text-white font-medium">+ New</Text>
            </TouchableOpacity>
          </View>

          {/* Search Bar */}
          <View className="bg-gray-50 border border-gray-200 rounded-lg px-4 py-3 mb-4">
            <View className="flex-row items-center">
              <IconSymbol name="magnifyingglass" size={20} color="#6b7280" />
              <TextInput
                className="flex-1 ml-3 text-gray-900"
                placeholder="Search appointments..."
                placeholderTextColor="#9ca3af"
                value={searchQuery}
                onChangeText={setSearchQuery}
              />
            </View>
          </View>

          {/* Filter Buttons */}
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <View className="flex-row">
              <FilterButton title="All" value="all" count={appointmentsData.length} />
              <FilterButton title="Confirmed" value="confirmed" count={confirmedCount} />
              <FilterButton title="Pending" value="pending" count={pendingCount} />
            </View>
          </ScrollView>
        </View>

        {/* Appointments List */}
        <ScrollView className="flex-1 px-4 py-4" showsVerticalScrollIndicator={false}>
          {filteredAppointments.length > 0 ? (
            filteredAppointments.map((appointment) => (
              <AppointmentCard key={appointment.id} appointment={appointment} />
            ))
          ) : (
            <View className="flex-1 items-center justify-center py-12">
              <IconSymbol name="calendar.badge.exclamationmark" size={64} color="#d1d5db" />
              <Text className="text-gray-500 text-lg font-medium mt-4">No appointments found</Text>
              <Text className="text-gray-400 text-center mt-2">
                {searchQuery ? 'Try adjusting your search terms' : 'Create your first appointment to get started'}
              </Text>
            </View>
          )}
        </ScrollView>
      </View>
    </SafeAreaView>
  );
}
