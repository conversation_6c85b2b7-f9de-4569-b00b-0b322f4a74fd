import { IconSymbol } from '@/components/ui/IconSymbol';
import { useUser } from '@/contexts/UserContext';
import React, { useEffect, useState } from 'react';
import {
  ActivityIndicator,
    SafeAreaView,
    ScrollView,
    StatusBar,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import APP_URL from '@/api/index'
// Mock performance data for Sales Representative
const performanceData = {
  overview: {
    totalAppointments: 45,
    completedAppointments: 38,
    conversionRate: 84.4,
    totalRevenue: 12600,
    avgSaleValue: 332,
    profitMargin: 35.2,
  },
  weeklyStats: [
    { week: 'Week 1', appointments: 12, sales: 10, revenue: 3200 },
    { week: 'Week 2', appointments: 11, sales: 9, revenue: 2800 },
    { week: 'Week 3', appointments: 10, sales: 8, revenue: 3100 },
    { week: 'Week 4', appointments: 12, sales: 11, revenue: 3500 },
  ],
  topItems: [
    { category: 'Bijoux', sales: 15, revenue: 4500, margin: 38 },
    { category: 'Montres', sales: 8, revenue: 3200, margin: 42 },
    { category: 'Pièces de monnaie', sales: 12, revenue: 2800, margin: 28 },
    { category: 'Argenterie', sales: 6, revenue: 1800, margin: 35 },
  ],
  recentSales: [
    { date: '2024-01-15', client: 'Marie Dubois', items: 3, amount: 450, profit: 158 },
    { date: '2024-01-14', client: 'Jean Martin', items: 1, amount: 280, profit: 98 },
    { date: '2024-01-13', client: 'Sophie Laurent', items: 2, amount: 320, profit: 112 },
    { date: '2024-01-12', client: 'Pierre Moreau', items: 4, amount: 680, profit: 238 },
  ],
};

export default function RepresentativePerformanceScreen() {
  const { authToken, rankings, loadingRankings, weekInfo, fetchWeeklyRankings } = useUser();
  
  

  // useEffect(() => {
  //   fetchWeeklyRankings();
  // }, [authToken]);

  const StatCard = ({ title, value, subtitle, trend, color = 'blue' }) => (
    <View className="bg-white p-4 rounded-xl shadow-sm border border-gray-100 flex-1 mx-1">
      <View className="flex-row items-center justify-between mb-2">
        <Text className="text-gray-600 text-sm font-medium">{title}</Text>
        {trend && (
          <View className={`flex-row items-center px-2 py-1 rounded-full ${trend > 0 ? 'bg-green-100' : 'bg-red-100'}`}>
            <IconSymbol 
              name={trend > 0 ? 'arrow.up' : 'arrow.down'} 
              size={12} 
              color={trend > 0 ? '#059669' : '#dc2626'} 
            />
            <Text className={`text-xs font-medium ml-1 ${trend > 0 ? 'text-green-600' : 'text-red-600'}`}>
              {Math.abs(trend)}%
            </Text>
          </View>
        )}
      </View>
      <Text className={`text-xl font-bold text-${color}-600`}>{value}</Text>
      {subtitle && <Text className="text-gray-500 text-xs mt-1">{subtitle}</Text>}
    </View>
  );

  // const PeriodSelector = () => (
  //   <View className="flex-row bg-gray-100 rounded-lg p-1 mx-4 mb-6">
  //     {['week', 'month', 'quarter'].map((period) => (
  //       <TouchableOpacity
  //         key={period}
  //         className={`flex-1 py-2 px-4 rounded-md ${selectedPeriod === period ? 'bg-white shadow-sm' : ''}`}
  //         onPress={() => setSelectedPeriod(period)}
  //       >
  //         <Text className={`text-center font-medium capitalize ${selectedPeriod === period ? 'text-blue-600' : 'text-gray-600'}`}>
  //           {period}
  //         </Text>
  //       </TouchableOpacity>
  //     ))}
  //   </View>
  // );

  const WeeklyChart = () => (
    <View className="bg-white mx-4 p-4 rounded-xl shadow-sm border border-gray-100 mb-6">
      <Text className="text-lg font-semibold text-gray-900 mb-4">Weekly Performance</Text>
      <View className="space-y-3">
        {performanceData.weeklyStats.map((week, index) => (
          <View key={index} className="flex-row items-center">
            <Text className="text-gray-600 text-sm w-16">{week.week}</Text>
            <View className="flex-1 mx-3">
              <View className="bg-gray-200 h-2 rounded-full">
                <View 
                  className="bg-blue-500 h-2 rounded-full" 
                  style={{ width: `${(week.sales / week.appointments) * 100}%` }}
                />
              </View>
            </View>
            <Text className="text-gray-900 font-medium text-sm w-20 text-right">
              {week.sales}/{week.appointments}
            </Text>
            <Text className="text-green-600 font-semibold text-sm w-16 text-right">
              €{week.revenue}
            </Text>
          </View>
        ))}
      </View>
    </View>
  );

  const TopItemsSection = () => (
    <View className="bg-white mx-4 p-4 rounded-xl shadow-sm border border-gray-100 mb-6">
      <Text className="text-lg font-semibold text-gray-900 mb-4">Top Performing Categories</Text>
      <View className="space-y-3">
        {performanceData.topItems.map((item, index) => (
          <View key={index} className="flex-row items-center justify-between">
            <View className="flex-1">
              <Text className="text-gray-900 font-medium">{item.category}</Text>
              <Text className="text-gray-500 text-sm">{item.sales} sales</Text>
            </View>
            <View className="items-end">
              <Text className="text-gray-900 font-semibold">€{item.revenue}</Text>
              <Text className="text-green-600 text-sm">{item.margin}% margin</Text>
            </View>
          </View>
        ))}
      </View>
    </View>
  );

  const RecentSalesSection = () => (
    <View className="bg-white mx-4 p-4 rounded-xl shadow-sm border border-gray-100 mb-6">
      <Text className="text-lg font-semibold text-gray-900 mb-4">Recent Sales</Text>
      <View className="space-y-3">
        {performanceData.recentSales.map((sale, index) => (
          <View key={index} className="flex-row items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
            <View className="flex-1">
              <Text className="text-gray-900 font-medium">{sale.client}</Text>
              <Text className="text-gray-500 text-sm">{sale.date} • {sale.items} items</Text>
            </View>
            <View className="items-end">
              <Text className="text-gray-900 font-semibold">€{sale.amount}</Text>
              <Text className="text-green-600 text-sm">+€{sale.profit} profit</Text>
            </View>
          </View>
        ))}
      </View>
    </View>
  );

  const TeamRankingsSection = () => (
    <View className="bg-white mx-4 p-4 rounded-xl shadow-sm border border-gray-100 mb-6">
      <View className="flex items-center justify-between mb-4">
        <Text className="text-lg font-semibold text-gray-900">Team Rankings</Text>
        <View className="flex-row items-center">
          {weekInfo && (
            <Text className="text-gray-500 text-sm mr-3">
              Week {weekInfo.week_number} • {new Date(weekInfo.start).toLocaleDateString()} - {new Date(weekInfo.end).toLocaleDateString()}
            </Text>
          )}
          <TouchableOpacity
            onPress={fetchWeeklyRankings}
            disabled={loadingRankings}
            className="p-2 rounded-lg bg-blue-50"
          >
            <IconSymbol
              name="arrow.clockwise"
              size={16}
              color={loadingRankings ? "#9ca3af" : "#3b82f6"}
            />
          </TouchableOpacity>
        </View>
      </View>

      {loadingRankings ? (
        <View className="flex-row justify-center py-8">
          <ActivityIndicator size="small" color="#3b82f6" />
        </View>
      ) : rankings.length > 0 ? (
        <View className="space-y-3">
          {rankings.map((ranking, index) => (
            <View key={ranking.representative.id} className="flex-row items-center py-3 border-b border-gray-100 last:border-b-0">
              {/* Rank Badge */}
              <View className={`w-8 h-8 rounded-full items-center justify-center mr-3 ${
                ranking.rank === 1 ? 'bg-yellow-100' :
                ranking.rank === 2 ? 'bg-gray-100' :
                ranking.rank === 3 ? 'bg-orange-100' : 'bg-blue-50'
              }`}>
                <Text className={`font-bold text-sm ${
                  ranking.rank === 1 ? 'text-yellow-600' :
                  ranking.rank === 2 ? 'text-gray-600' :
                  ranking.rank === 3 ? 'text-orange-600' : 'text-blue-600'
                }`}>
                  {ranking.badges?.rank_badge || ranking.rank}
                </Text>
              </View>

              {/* Representative Info */}
              <View className="flex-1">
                <View className="flex-row items-center mb-1">
                  <Text className="text-gray-900 font-medium mr-2">{ranking.representative.name}</Text>
                  {ranking.badges?.top_performer && (
                    <View className="bg-green-100 px-2 py-1 rounded-full">
                      <Text className="text-green-600 text-xs font-medium">Top Performer</Text>
                    </View>
                  )}
                </View>
                <Text className="text-gray-500 text-sm">
                  {ranking.performance.completed_appointments}/{ranking.performance.total_appointments} appointments • {ranking.performance.completion_rate.toFixed(1)}% rate
                </Text>
              </View>

              {/* Revenue */}
              <View className="items-end">
                <Text className="text-gray-900 font-semibold">€{ranking.performance.weekly_revenue.toFixed(2)}</Text>
                <Text className="text-gray-500 text-xs">Weekly Revenue</Text>
              </View>
            </View>
          ))}
        </View>
      ) : (
        <View className="py-8 items-center">
          <Text className="text-gray-500">No rankings data available</Text>
        </View>
      )}
    </View>
  );

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <StatusBar barStyle="dark-content" backgroundColor="#f9fafb" />
      
      {/* Header */}
      <View className="bg-white px-4 py-6 border-b border-gray-100">
        <Text className="text-2xl font-bold text-gray-900 mb-2">Performance Dashboard</Text>
        <Text className="text-gray-600">Track your sales performance and metrics</Text>
      </View>

      <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
        {/* Period Selector */}
        <View className="py-4">
          {/* <PeriodSelector /> */}
        </View>

        {/* Overview Stats */}
        <View className="px-4 mb-6">
          <View className="flex-row mb-4">
            <StatCard 
              title="Total Appointments" 
              value={performanceData.overview.totalAppointments}
              subtitle="This month"
              trend={12}
              color="blue"
            />
            <StatCard 
              title="Conversion Rate" 
              value={`${performanceData.overview.conversionRate}%`}
              subtitle="Success rate"
              trend={5.2}
              color="green"
            />
          </View>
          <View className="flex-row mb-4">
            <StatCard 
              title="Total Revenue" 
              value={`€${performanceData.overview.totalRevenue}`}
              subtitle="This month"
              trend={8.7}
              color="purple"
            />
            <StatCard 
              title="Avg Sale Value" 
              value={`€${performanceData.overview.avgSaleValue}`}
              subtitle="Per transaction"
              trend={-2.1}
              color="orange"
            />
          </View>
          <View className="flex-row">
            <StatCard 
              title="Profit Margin" 
              value={`${performanceData.overview.profitMargin}%`}
              subtitle="Average margin"
              trend={3.4}
              color="indigo"
            />
            <StatCard 
              title="Completed" 
              value={performanceData.overview.completedAppointments}
              subtitle="Appointments"
              trend={15}
              color="teal"
            />
          </View>
        </View>

        {/* Weekly Performance Chart */}
        {/* <WeeklyChart /> */}

        {/* Team Rankings */}
        <TeamRankingsSection />

        {/* Top Performing Categories */}
        <TopItemsSection />

        {/* Recent Sales */}
        <RecentSalesSection />

        {/* Goals Section */}
        <View className="bg-white mx-4 p-4 rounded-xl shadow-sm border border-gray-100 mb-6">
          <Text className="text-lg font-semibold text-gray-900 mb-4">Monthly Goals</Text>
          <View className="space-y-4">
            <View>
              <View className="flex-row justify-between mb-2">
                <Text className="text-gray-600">Revenue Target</Text>
                <Text className="text-gray-900 font-medium">€12,600 / €15,000</Text>
              </View>
              <View className="bg-gray-200 h-2 rounded-full">
                <View className="bg-green-500 h-2 rounded-full" style={{ width: '84%' }} />
              </View>
            </View>
            <View>
              <View className="flex-row justify-between mb-2">
                <Text className="text-gray-600">Appointments Target</Text>
                <Text className="text-gray-900 font-medium">45 / 50</Text>
              </View>
              <View className="bg-gray-200 h-2 rounded-full">
                <View className="bg-blue-500 h-2 rounded-full" style={{ width: '90%' }} />
              </View>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
