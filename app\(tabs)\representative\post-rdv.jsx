import { IconSymbol } from "@/components/ui/IconSymbol";
import { useAppointments } from "@/contexts/AppointmentsContext";
import { router, useLocalSearchParams } from "expo-router";
import React, { useCallback, useEffect, useState } from "react";
import {
  ActivityIndicator,
  Alert,
  SafeAreaView,
  ScrollView,
  StatusBar,
  Switch,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";

const itemTypes = [
  "Bijoux",
  "Montres",
  "Pièces de monnaie",
  "Argenterie",
  "Antiquités",
  "Objets d'art",
  "Autres",
];
const ItemForm = React.memo(
  ({
    item,
    index,
    onUpdateItem,
    onRemoveItem,
    canRemove,
    appointmentData,
    calculateBenefit,
  }) => (
    <View className="bg-gray-50 p-4 rounded-lg mb-4">
      <View className="flex-row items-center justify-between mb-3">
        <View className="flex-1">
          <Text className="text-lg font-semibold text-gray-900">
            Article {index + 1}
          </Text>
          {appointmentData?.items_collection &&
            appointmentData.items_collection[index] && (
              <Text className="text-sm text-blue-600 mt-1">
                Prévu:{" "}
                {typeof appointmentData.items_collection[index] === "string"
                  ? appointmentData.items_collection[index]
                  : appointmentData.items_collection[index].description ||
                    appointmentData.items_collection[index].type}
              </Text>
            )}
        </View>
        <View className="flex-row items-center space-x-2">
          <View className="items-center">
            <Switch
              value={item.purchased}
              onValueChange={(value) =>
                onUpdateItem(item.id, "purchased", value)
              }
              trackColor={{ false: "#f3f4f6", true: "#10b981" }}
              thumbColor={item.purchased ? "#ffffff" : "#ffffff"}
            />
            <Text className="text-xs text-gray-600 mt-1">
              {item.purchased ? "Acheté" : "Non acheté"}
            </Text>
          </View>
          {canRemove && (
            <TouchableOpacity
              onPress={() => onRemoveItem(item.id)}
              className="w-8 h-8 bg-red-100 rounded-full items-center justify-center ml-2"
            >
              <IconSymbol name="trash" size={16} color="#dc2626" />
            </TouchableOpacity>
          )}
        </View>
      </View>

      <View className="space-y-3">
        <View>
          <Text className="text-gray-700 font-medium mb-1">
            Type d'article *
          </Text>
          <View className="bg-white border border-gray-200 rounded-lg">
            <TextInput
              className="px-4 py-3 text-gray-900"
              placeholder="Ex: Bijoux, Montres, Pièces de monnaie..."
              value={item.itemType}
              onChangeText={(value) => onUpdateItem(item.id, "itemType", value)}
            />
          </View>
          <Text className="text-gray-500 text-xs mt-1">
            Types courants: {itemTypes.join(", ")}
          </Text>
        </View>

        <View>
          <Text className="text-gray-700 font-medium mb-1">Description *</Text>
          <TextInput
            className="bg-white border border-gray-200 rounded-lg px-4 py-3 text-gray-900"
            placeholder="Description détaillée de l'article"
            value={item.description}
            onChangeText={(value) =>
              onUpdateItem(item.id, "description", value)
            }
            multiline
            numberOfLines={2}
          />
        </View>

        <View className="flex-row space-x-3">
          <View className="flex-1">
            <Text className="text-gray-700 font-medium mb-1">Poids (g)</Text>
            <TextInput
              className="bg-white border border-gray-200 rounded-lg px-4 py-3 text-gray-900"
              placeholder="0"
              value={item.weight}
              onChangeText={(value) => onUpdateItem(item.id, "weight", value)}
              keyboardType="numeric"
            />
          </View>
          <View className="flex-1">
            <Text className="text-gray-700 font-medium mb-1">
              Prix d'achat (€) {item.purchased ? "*" : "(optionnel)"}
            </Text>
            <TextInput
              className={`bg-white border rounded-lg px-4 py-3 text-gray-900 ${
                item.purchased && !item.purchasePrice
                  ? "border-red-300"
                  : "border-gray-200"
              }`}
              placeholder={item.purchased ? "Prix payé au client" : "0.00"}
              value={item.purchasePrice}
              onChangeText={(value) =>
                onUpdateItem(item.id, "purchasePrice", value)
              }
              keyboardType="numeric"
              editable={item.purchased}
              style={{ opacity: item.purchased ? 1 : 0.6 }}
            />
            {item.purchased && !item.purchasePrice && (
              <Text className="text-red-500 text-xs mt-1">
                Prix d'achat requis pour les articles achetés
              </Text>
            )}
          </View>
        </View>

        <View className="flex-row space-x-3">
          <View className="flex-1">
            <Text className="text-gray-700 font-medium mb-1">
              Valeur estimée (€) *
            </Text>
            <TextInput
              className="bg-white border border-gray-200 rounded-lg px-4 py-3 text-gray-900"
              placeholder="0.00"
              value={item.estimatedValue}
              onChangeText={(value) =>
                onUpdateItem(item.id, "estimatedValue", value)
              }
              keyboardType="numeric"
            />
          </View>
          <View className="flex-1">
            <Text className="text-gray-700 font-medium mb-1">
              Bénéfice individuel
            </Text>
            <View className="bg-gray-100 border border-gray-200 rounded-lg px-4 py-3">
              <Text
                className={`font-semibold ${calculateBenefit(item) >= 0 ? "text-green-600" : "text-red-600"}`}
              >
                €{calculateBenefit(item).toFixed(2)}
              </Text>
            </View>
          </View>
        </View>
      </View>
    </View>
  )
);
export default function PostRDVScreen() {
  const params = useLocalSearchParams();
  const appointmentData = params.appointmentData
    ? JSON.parse(params.appointmentData)
    : null;
  const appointmentId = params.appointmentId;

  const { isLoading, error, storePurchase, clearError } = useAppointments();
  const formatTime = (dateString) => {
    const date = new Date(dateString);
    const hours = String(date.getUTCHours()).padStart(2, "0");
    const minutes = String(date.getUTCMinutes()).padStart(2, "0");
    return `${hours}:${minutes}`;
  };
  // Initialize state with appointment data
  const [formData, setFormData] = useState(() => ({
    clientName: appointmentData?.client_name || "",
    appointmentDate: appointmentData?.date_time
      ? new Date(appointmentData.date_time).toISOString().split("T")[0]
      : "",
    appointmentTime: appointmentData?.date_time
      ? formatTime(appointmentData.date_time)
      : "",
    itemsCollection: appointmentData?.items_collection || [],
    meetingNotes: "",
  }));

  const [items, setItems] = useState(() => {
    if (
      appointmentData?.items_collection &&
      appointmentData.items_collection.length > 0
    ) {
      return appointmentData.items_collection.map((item, index) => ({
        id: `${appointmentId}_${index + 1}`,
        itemType: typeof item === "string" ? item : item.type || "",
        description: typeof item === "string" ? item : item.description || "",
        weight: "",
        purchasePrice: "",
        estimatedValue: "",
        purchased: false,
      }));
    }
    return [
      {
        id: `${appointmentId}_1`,
        itemType: "",
        description: "",
        weight: "",
        purchasePrice: "",
        estimatedValue: "",
        purchased: false,
      },
    ];
  });

  // Reset form when appointment ID changes (only when switching appointments)
  useEffect(() => {
    setFormData(() => ({
      clientName: appointmentData?.client_name || "",
      appointmentDate: appointmentData?.date_time
        ? new Date(appointmentData.date_time).toISOString().split("T")[0]
        : "",
      appointmentTime: appointmentData?.date_time
        ? formatTime(appointmentData.date_time)
        : "",
      itemsCollection: appointmentData?.items_collection || [],
      meetingNotes: "",
    }));

    setItems(() => {
      if (
        appointmentData?.items_collection &&
        appointmentData.items_collection.length > 0
      ) {
        return appointmentData.items_collection.map((item, index) => ({
          id: `${appointmentId}_${index + 1}`,
          itemType: typeof item === "string" ? item : item.type || "",
          description: typeof item === "string" ? item : item.description || "",
          weight: "",
          purchasePrice: "",
          estimatedValue: "",
          purchased: false,
        }));
      }
      return [
        {
          id: `${appointmentId}_1`,
          itemType: "",
          description: "",
          weight: "",
          purchasePrice: "",
          estimatedValue: "",
          purchased: false,
        },
      ];
    });
  }, [appointmentId]); // Only depend on appointmentId to prevent infinite loops

  // Show error alert if there's an API error
  useEffect(() => {
    if (error) {
      Alert.alert("Erreur", error, [{ text: "OK", onPress: clearError }]);
    }
  }, [error]);

  const addItem = useCallback(() => {
    const newItem = {
      id: `${appointmentId}_${Date.now()}`,
      itemType: "",
      description: "",
      weight: "",
      purchasePrice: "",
      estimatedValue: "",
      purchased: false,
    };
    setItems((prevItems) => [...prevItems, newItem]);
  }, [appointmentId]);

  const removeItem = useCallback((id) => {
    setItems((prevItems) => {
      if (prevItems.length > 1) {
        return prevItems.filter((item) => item.id !== id);
      }
      return prevItems;
    });
  }, []);

  const updateItem = useCallback((id, field, value) => {
    setItems((prevItems) =>
      prevItems.map((item) =>
        item.id === id ? { ...item, [field]: value } : item
      )
    );
  }, []);

  const calculateItemBenefit = useCallback((item) => {
    if (!item.purchased || !item.estimatedValue || !item.purchasePrice)
      return 0;
    return parseFloat(item.estimatedValue) - parseFloat(item.purchasePrice);
  }, []);

  const calculateTotals = useCallback(() => {
    const purchasedItems = items.filter((item) => item.purchased);
    const totalPurchasePrice = purchasedItems.reduce(
      (sum, item) => sum + (parseFloat(item.purchasePrice) || 0),
      0
    );
    const totalEstimatedValue = purchasedItems.reduce(
      (sum, item) => sum + (parseFloat(item.estimatedValue) || 0),
      0
    );
    const estimatedProfit = totalEstimatedValue - totalPurchasePrice;
    const profitMargin =
      totalEstimatedValue > 0
        ? (estimatedProfit / totalEstimatedValue) * 100
        : 0;

    return {
      totalPurchasePrice,
      totalEstimatedValue,
      estimatedProfit,
      profitMargin,
    };
  }, [items]);

  const handleSubmit = async () => {
    // Validation
    if (
      !formData.clientName ||
      !formData.appointmentDate ||
      !formData.appointmentTime
    ) {
      Alert.alert("Erreur", "Veuillez remplir tous les champs obligatoires");
      return;
    }

    // Check if at least one item has been evaluated (purchased or not)
    const evaluatedItems = items.filter(
      (item) =>
        item.itemType &&
        item.description &&
        (item.purchased || item.estimatedValue)
    );

    if (evaluatedItems.length === 0) {
      Alert.alert("Attention", "Veuillez évaluer au moins un article");
      return;
    }

    try {
      // Submit individual purchase records for each evaluated item
      const purchasePromises = evaluatedItems.map(async (item, index) => {
        // Validate required fields
        if (!item.description || !item.itemType) {
          throw new Error(
            `Article ${index + 1}: Description et type d'article sont requis`
          );
        }

        if (item.purchased && !item.purchasePrice) {
          throw new Error(
            `Article ${index + 1}: Prix d'achat requis pour les articles achetés`
          );
        }

        const purchaseData = {
          appointment_id: parseInt(params.appointmentId),
          status: item.purchased ? "purchased" : "not_purchased",
          description: item.description.trim(),
          item_type: item.itemType.trim(),
          weight: parseFloat(item.weight) || 0,
          buy_price: item.purchased ? parseFloat(item.purchasePrice) || 0 : 0,
          resale_price: parseFloat(item.estimatedValue) || 0,
          benefit: item.purchased ? calculateItemBenefit(item) : 0,
          notes: formData.meetingNotes?.trim() || "",
        };

        console.log("Individual purchase data:", purchaseData);
        return await storePurchase(purchaseData);
      });

      // Wait for all purchase records to be submitted
      const purchaseResults = await Promise.allSettled(purchasePromises);

      // Check if any purchases failed
      const failedPurchases = purchaseResults.filter(
        (result) => result.status === "rejected"
      );

      if (failedPurchases.length > 0) {
        console.warn("Some purchase records failed:", failedPurchases);
        Alert.alert(
          "Avertissement",
          `${purchaseResults.length - failedPurchases.length} achat(s) enregistré(s), mais ${failedPurchases.length} ont échoué`,
          [
            {
              text: "OK",
              onPress: () => router.back(),
            },
          ]
        );
      } else {
        Alert.alert(
          "Succès",
          `Tous les achats ont été enregistrés avec succès (${purchaseResults.length} article(s))`,
          [
            {
              text: "OK",
              onPress: () => router.back(),
            },
          ]
        );
      }
    } catch (err) {
      console.error("Error submitting Post-RDV:", err);
      Alert.alert("Erreur", "Une erreur est survenue lors de l'enregistrement");
    }
  };

  const totals = calculateTotals();

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <StatusBar barStyle="dark-content" backgroundColor="#f9fafb" />

      {/* Header */}
      <View className="bg-white px-4 py-4 border-b border-gray-100 flex-row items-center">
        <TouchableOpacity onPress={() => router.back()} className="mr-4">
          <IconSymbol name="chevron.left" size={24} color="#374151" />
        </TouchableOpacity>
        <View className="flex-1">
          <Text className="text-xl font-bold text-gray-900">Post-RDV</Text>
          <Text className="text-gray-600">Rapport post-rendez-vous</Text>
        </View>
      </View>

      <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
        {/* Appointment Info */}
        <View className="bg-white mx-4 mt-4 p-4 rounded-xl shadow-sm border border-gray-100">
          <Text className="text-lg font-semibold text-gray-900 mb-4">
            Informations du rendez-vous
          </Text>

          <View className="space-y-3">
            <View>
              <Text className="text-gray-700 font-medium mb-1">
                Nom du client *
              </Text>
              <TextInput
                className="bg-gray-50 border border-gray-200 rounded-lg px-4 py-3 text-gray-900"
                value={formData.clientName}
                onChangeText={(value) =>
                  setFormData({ ...formData, clientName: value })
                }
                editable={false}
              />
            </View>

            <View className="flex-row space-x-3">
              <View className="flex-1">
                <Text className="text-gray-700 font-medium mb-1">
                  Date du rendez-vous *
                </Text>
                <TextInput
                  className="bg-gray-50 border border-gray-200 rounded-lg px-4 py-3 text-gray-900"
                  value={formData.appointmentDate}
                  onChangeText={(value) =>
                    setFormData({ ...formData, appointmentDate: value })
                  }
                  editable={false}
                />
              </View>
              <View className="flex-1">
                <Text className="text-gray-700 font-medium mb-1">
                  Heure du rendez-vous *
                </Text>
                <TextInput
                  className="bg-gray-50 border border-gray-200 rounded-lg px-4 py-3 text-gray-900"
                  value={formData.appointmentTime}
                  onChangeText={(value) =>
                    setFormData({ ...formData, appointmentTime: value })
                  }
                  editable={false}
                />
              </View>
            </View>

            <View>
              <Text className="text-gray-700 font-medium mb-1">
                Articles à collecter (prévus)
              </Text>
              <View className="bg-gray-50 border border-gray-200 rounded-lg p-3">
                <Text className="text-gray-600">
                  {formData.itemsCollection.join(", ")}
                </Text>
              </View>
            </View>
          </View>
        </View>

        {/* Items Section */}
        <View className="mx-4 mt-4">
          <View className="flex-row items-center justify-between mb-4">
            <View className="flex-1">
              <Text className="text-lg font-semibold text-gray-900">
                Articles évalués
              </Text>
              {appointmentData?.items_collection &&
                appointmentData.items_collection.length > 0 && (
                  <Text className="text-sm text-gray-600 mt-1">
                    {appointmentData.items_collection.length} article(s)
                    prévu(s) dans le rendez-vous
                  </Text>
                )}
            </View>
            <TouchableOpacity
              onPress={addItem}
              className="bg-blue-600 px-4 py-2 rounded-lg flex-row items-center"
            >
              <IconSymbol name="plus" size={16} color="#ffffff" />
              <Text className="text-white font-medium ml-1">Ajouter</Text>
            </TouchableOpacity>
          </View>

          {items.map((item, index) => (
            <ItemForm
              key={item.id}
              item={item}
              index={index}
              onUpdateItem={updateItem}
              onRemoveItem={removeItem}
              canRemove={items.length > 1}
              appointmentData={appointmentData}
              calculateBenefit={calculateItemBenefit}
            />
          ))}
        </View>

        {/* Totals Section */}
        <View className="bg-white mx-4 mt-4 p-4 rounded-xl shadow-sm border border-gray-100">
          <Text className="text-lg font-semibold text-gray-900 mb-4">
            Récapitulatif financier
          </Text>

          {/* Summary Stats */}
          <View className="flex-row justify-between mb-4 p-3 bg-gray-50 rounded-lg">
            <View className="items-center">
              <Text className="text-2xl font-bold text-blue-600">
                {items.length}
              </Text>
              <Text className="text-gray-600 text-sm">Articles évalués</Text>
            </View>
            <View className="items-center">
              <Text className="text-2xl font-bold text-green-600">
                {items.filter((item) => item.purchased).length}
              </Text>
              <Text className="text-gray-600 text-sm">Achetés</Text>
            </View>
            <View className="items-center">
              <Text className="text-2xl font-bold text-orange-600">
                {
                  items.filter((item) => !item.purchased && item.estimatedValue)
                    .length
                }
              </Text>
              <Text className="text-gray-600 text-sm">Refusés</Text>
            </View>
          </View>

          <View className="space-y-3">
            <View className="flex-row justify-between">
              <Text className="text-gray-600">Prix d'achat total:</Text>
              <Text className="font-semibold text-gray-900">
                €{totals.totalPurchasePrice.toFixed(2)}
              </Text>
            </View>
            <View className="flex-row justify-between">
              <Text className="text-gray-600">Valeur estimée totale:</Text>
              <Text className="font-semibold text-gray-900">
                €{totals.totalEstimatedValue.toFixed(2)}
              </Text>
            </View>
            <View className="flex-row justify-between border-t border-gray-200 pt-3">
              <Text className="text-gray-900 font-medium">
                Bénéfice estimé:
              </Text>
              <Text
                className={`font-bold text-lg ${totals.estimatedProfit >= 0 ? "text-green-600" : "text-red-600"}`}
              >
                €{totals.estimatedProfit.toFixed(2)}
              </Text>
            </View>
            <View className="flex-row justify-between">
              <Text className="text-gray-600">Marge bénéficiaire:</Text>
              <Text
                className={`font-semibold ${totals.profitMargin >= 0 ? "text-green-600" : "text-red-600"}`}
              >
                {totals.profitMargin.toFixed(1)}%
              </Text>
            </View>
          </View>
        </View>

        {/* Meeting Notes */}
        <View className="bg-white mx-4 mt-4 p-4 rounded-xl shadow-sm border border-gray-100">
          <Text className="text-lg font-semibold text-gray-900 mb-3">
            Notes de réunion
          </Text>
          <TextInput
            className="bg-gray-50 border border-gray-200 rounded-lg px-4 py-3 text-gray-900"
            placeholder="Observations détaillées sur le rendez-vous..."
            value={formData.meetingNotes}
            onChangeText={(value) =>
              setFormData({ ...formData, meetingNotes: value })
            }
            multiline
            numberOfLines={4}
            textAlignVertical="top"
          />
        </View>

        {/* Submit Button */}
        <View className="px-4 py-6">
          <TouchableOpacity
            onPress={handleSubmit}
            disabled={isLoading}
            className={`py-4 rounded-xl flex-row items-center justify-center ${isLoading ? "bg-gray-400" : "bg-blue-600"}`}
          >
            {isLoading ? (
              <>
                <ActivityIndicator size="small" color="#ffffff" />
                <Text className="text-white font-semibold text-lg ml-2">
                  Enregistrement...
                </Text>
              </>
            ) : (
              <>
                <IconSymbol name="checkmark.circle" size={20} color="#ffffff" />
                <Text className="text-white font-semibold text-lg ml-2">
                  Enregistrer Post-RDV
                </Text>
              </>
            )}
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
