import { IconSymbol } from "@/components/ui/IconSymbol";
import { useAppointments } from "@/contexts/AppointmentsContext";
import { useUser } from "@/contexts/UserContext";
import { router } from "expo-router";
import React, { useEffect, useState } from "react";
import {
    ActivityIndicator,
    Alert,
    SafeAreaView,
    ScrollView,
    StatusBar,
    Text,
    TextInput,
    TouchableOpacity,
    View,
} from "react-native";

// Mock appointments data
const mockAppointments = {
  "2024-01-15": [
    {
      id: 1,
      time: "09:00",
      clientName: "Marie Dub<PERSON>",
      clientPhone: "+33 6 12 34 56 78",
      clientAddress: "15 Rue de la Paix, Paris",
      source: "outbound",
      appointmentType: "announced",
      itemsCollection: ["Bijoux anciens", "Montre en or"],
      notes: "Cliente intéressée par vente rapide",
      status: "scheduled",
    },
    {
      id: 2,
      time: "11:30",
      clientName: "<PERSON>",
      clientPhone: "+33 6 98 76 54 32",
      clientAddress: "42 Avenue des Champs, Lyon",
      source: "leboncoin",
      appointmentType: "not_announced",
      itemsCollection: ["Argenterie", "Pièces de monnaie"],
      notes: "Rendez-vous confirmé par téléphone",
      status: "scheduled",
    },
    {
      id: 3,
      time: "14:00",
      clientName: "Sophie Laurent",
      clientPhone: "+33 6 55 44 33 22",
      clientAddress: "8 Place du Marché, Marseille",
      source: "outbound",
      appointmentType: "announced",
      itemsCollection: ["Bijoux fantaisie", "Montres"],
      notes: "Première visite, très motivée",
      status: "completed",
    },
    {
      id: 4,
      time: "16:30",
      clientName: "Pierre Moreau",
      clientPhone: "+33 6 11 22 33 44",
      clientAddress: "23 Rue Victor Hugo, Toulouse",
      source: "leboncoin",
      appointmentType: "not_announced",
      itemsCollection: ["Antiquités", "Objets d'art"],
      notes: "Collection importante à évaluer",
      status: "scheduled",
    },
  ],
  "2024-01-16": [
    {
      id: 5,
      time: "10:00",
      clientName: "Claire Dubois",
      clientPhone: "+33 6 77 88 99 00",
      clientAddress: "56 Boulevard Saint-Germain, Paris",
      source: "outbound",
      appointmentType: "announced",
      itemsCollection: ["Bijoux en or", "Montres de luxe"],
      notes: "Rendez-vous de suivi",
      status: "scheduled",
    },
    {
      id: 6,
      time: "15:00",
      clientName: "Michel Rousseau",
      clientPhone: "+33 6 33 44 55 66",
      clientAddress: "12 Rue de la République, Nice",
      source: "leboncoin",
      appointmentType: "not_announced",
      itemsCollection: ["Pièces rares", "Médailles"],
      notes: "Client collectionneur expérimenté",
      status: "scheduled",
    },
  ],
};

export default function RepresentativeAppointmentsScreen() {
  const today = new Date().toISOString().split("T")[0];
  const [selectedDate, setSelectedDate] = useState(today);
  const [searchQuery, setSearchQuery] = useState("");
  const [appointmentsData, setAppointmentsData] = useState([]);
  const {
    authToken,
    rankings,
    loadingRankings,
    weekInfo,
    fetchWeeklyRankings,
  } = useUser();
  const {
    appointments,
    isLoading,
    error,
    fetchAppointmentsByDate,
    clearError,
  } = useAppointments();

  // Load today's appointments on component mount
  useEffect(() => {
    loadTodaysAppointments();
  }, []);

  // Fetch appointments when date changes (for filtering)
  useEffect(() => {
    if (selectedDate !== today) {
      loadAppointments();
    }
  }, [selectedDate]);

  // Show error alert if there's an API error
  useEffect(() => {
    if (error) {
      Alert.alert("Erreur", error, [{ text: "OK", onPress: clearError }]);
    }
  }, [error]);

  const loadTodaysAppointments = async () => {
    try {
      const response = await fetchAppointmentsByDate(today);
      if (response?.data) {
        // Sort appointments by dateTime for today
        const sortedAppointments = response.data.sort(
          (a, b) => new Date(a.date_time) - new Date(b.date_time)
        );
        setAppointmentsData(sortedAppointments);
      } else {
        setAppointmentsData([]);
      }
    } catch (err) {
      console.error("Error loading today's appointments:", err);
      setAppointmentsData([]);
    }
  };

  const loadAppointments = async () => {
    try {
      const response = await fetchAppointmentsByDate(selectedDate);
      if (response?.data) {
        // Sort appointments by dateTime
        const sortedAppointments = response.data.sort(
          (a, b) => new Date(a.date_time) - new Date(b.date_time)
        );
        setAppointmentsData(sortedAppointments);
      } else {
        setAppointmentsData([]);
      }
    } catch (err) {
      console.error("Error loading appointments:", err);
      setAppointmentsData([]);
    }
  };

  const handleDateChange = (newDate) => {
    setSelectedDate(newDate);
    setSearchQuery(""); // Clear search when changing date
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("fr-FR", {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const formatTime = (dateString) => {
    const date = new Date(dateString);
    const hours = String(date.getUTCHours()).padStart(2, "0");
    const minutes = String(date.getUTCMinutes()).padStart(2, "0");
    return `${hours}:${minutes}`;
  };

  const getStatusColor = (status) => {
    switch (status) {
      case "filled":
        return "bg-blue-100 text-blue-800";
      case "pending":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case "filled":
        return "Terminé";
      case "pending":
        return "en attente";
      default:
        return "Inconnu";
    }
  };

  const filteredAppointments = appointmentsData.filter(
    (appointment) =>
      appointment.client_name
        ?.toLowerCase()
        .includes(searchQuery.toLowerCase()) ||
      appointment.client_address
        ?.toLowerCase()
        .includes(searchQuery.toLowerCase())
  );

  // Check if appointment has Post-RDV filled
  const hasPostRDV = (appointment) => {
    return appointment.status === "filled" || appointment.post_rdv;
  };

  const handleAppointmentPress = (appointment) => {
    // Check if appointment already has Post-RDV filled
    if (hasPostRDV(appointment)) {
      Alert.alert("Information", "Ce rendez-vous a déjà un Post-RDV rempli.", [
        {
          text: "Annuler",
          style: "cancel",
        },
        {
          text: "Voir quand même",
          onPress: () => {
            router.push({
              pathname: "/(tabs)/representative/post-rdv",
              params: {
                appointmentId: appointment.id,
                appointmentData: JSON.stringify(appointment),
              },
            });
          },
        },
      ]);
      return;
    }

    // Navigate to Post-RDV form for appointments without Post-RDV
    router.push({
      pathname: "/(tabs)/representative/post-rdv",
      params: {
        appointmentId: appointment.id,
        appointmentData: JSON.stringify(appointment),
      },
    });
  };

  const AppointmentCard = ({ appointment }) => (
    <TouchableOpacity
      className={`bg-white p-4 rounded-xl shadow-sm mb-3 ${
        hasPostRDV(appointment)
          ? "border-2 border-blue-500"
          : "border border-gray-100"
      }`}
      onPress={() => handleAppointmentPress(appointment)}
    >
      <View className="flex-row items-start justify-between mb-3">
        <View className="flex-1">
          <View className="flex-row items-center mb-2">
            <Text className="text-lg font-semibold text-gray-900 mr-2">
              {appointment.client_name}
            </Text>
            <View
              className={`px-2 py-1 rounded-full ${getStatusColor(appointment.status)}`}
            >
              <Text className="text-xs font-medium">
                {getStatusText(appointment.status)}
              </Text>
            </View>
            {hasPostRDV(appointment) && (
              <View className="ml-2 px-2 py-1 rounded-full bg-blue-100">
                <Text className="text-xs font-medium text-blue-800">
                  Post-RDV
                </Text>
              </View>
            )}
          </View>
          <View className="flex-row items-center mb-1">
            <IconSymbol name="clock" size={16} color="#3b82f6" />
            <Text className="text-blue-600 font-medium text-base ml-1">
              {formatTime(appointment.date_time)}
            </Text>
            {selectedDate === today && (
              <Text className="text-gray-500 text-sm ml-2">
                (
                {new Date(appointment.date_time).getTime() > new Date().getTime()
                  ? "À venir"
                  : "Passé"}
                )
              </Text>
            )}
          </View>
        </View>
      </View>

      <View className="flex-row items-center mb-2">
        <IconSymbol name="location" size={16} color="#6b7280" />
        <Text className="text-gray-600 ml-2 flex-1">
          {appointment.client_address}
        </Text>
      </View>

      <View className="flex-row items-center mb-3">
        <IconSymbol name="phone" size={16} color="#6b7280" />
        <Text className="text-gray-600 ml-2">{appointment.client_phone}</Text>
      </View>

      <View className="mb-3">
        <Text className="text-gray-700 font-medium mb-1">
          Articles à collecter:
        </Text>
        <View className="flex-row flex-wrap">
          {(appointment.items_collection || []).map((item, index) => (
            <View
              key={index}
              className="bg-gray-100 px-2 py-1 rounded-md mr-2 mb-1"
            >
              <Text className="text-gray-700 text-sm">{item}</Text>
            </View>
          ))}
        </View>
      </View>

      {appointment.notes && (
        <View className="bg-yellow-50 p-3 rounded-lg mb-3">
          <Text className="text-yellow-800 text-sm">{appointment.notes}</Text>
        </View>
      )}

      <View className="flex-row items-center justify-between pt-3 border-t border-gray-100">
        <Text className="text-gray-500 text-sm">
          {appointment.source === "outbound"
            ? "Prospection sortante"
            : "LeBonCoin"}
        </Text>
        <View className="flex-row items-center">
          <Text className="text-blue-600 text-sm font-medium mr-1">
            {hasPostRDV(appointment) ? "Voir Post-RDV" : "Accéder au Post-RDV"}
          </Text>
          <IconSymbol name="chevron.right" size={16} color="#3b82f6" />
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <StatusBar barStyle="dark-content" backgroundColor="#f9fafb" />

      {/* Header */}
      <View className="bg-white px-4 py-6 border-b border-gray-100">
        <Text className="text-2xl font-bold text-gray-900 mb-2">
          Rendez-vous
        </Text>
        <Text className="text-gray-600">Gérez vos rendez-vous et Post-RDV</Text>
      </View>

      <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
        {/* Date Selector */}
        <View className="bg-white mx-4 mt-4 p-4 rounded-xl shadow-sm border border-gray-100">
          <Text className="text-lg font-semibold text-gray-900 mb-3">
            {selectedDate === today
              ? "Rendez-vous d'aujourd'hui"
              : "Filtrer par date"}
          </Text>
          <View className="flex-row items-center space-x-3">
            <View className="flex-1">
              <TextInput
                className="bg-gray-50 border border-gray-200 rounded-lg px-4 py-3 text-gray-900"
                placeholder="YYYY-MM-DD"
                value={selectedDate}
                onChangeText={handleDateChange}
              />
            </View>
            {selectedDate !== today && (
              <TouchableOpacity
                onPress={() => handleDateChange(today)}
                className="bg-blue-600 px-4 py-3 rounded-lg"
              >
                <Text className="text-white font-medium">Aujourd'hui</Text>
              </TouchableOpacity>
            )}
          </View>
          <Text className="text-gray-600 text-sm mt-2">
            {formatDate(selectedDate)}
            {selectedDate === today && " (Aujourd'hui)"}
          </Text>
        </View>

        {/* Search */}
        <View className="mx-4 mt-4">
          <View className="bg-white rounded-xl shadow-sm border border-gray-100  flex-row items-center px-4 py-3">
            <IconSymbol name="magnifyingglass" size={20} color="#6b7280" />
            <TextInput
              className="flex-1 ml-3 text-gray-900"
              placeholder="Rechercher par nom ou adresse..."
              value={searchQuery}
              onChangeText={setSearchQuery}
            />
          </View>
        </View>

        {/* Appointments Summary */}
        <View className="mx-4 mt-4 bg-white p-4 rounded-xl shadow-sm border border-gray-100">
          <Text className="text-lg font-semibold text-gray-900 mb-3">
            {selectedDate === today
              ? "Résumé d'aujourd'hui"
              : `Résumé du ${formatDate(selectedDate)}`}
          </Text>
          <View className="flex-row justify-between">
            <View className="items-center">
              <Text className="text-2xl font-bold text-blue-600">
                {filteredAppointments.length}
              </Text>
              <Text className="text-gray-600 text-sm">Total</Text>
            </View>
            <View className="items-center">
              <Text className="text-2xl font-bold text-green-600">
                {
                  filteredAppointments.filter(
                    (apt) => apt.status === "completed"
                  ).length
                }
              </Text>
              <Text className="text-gray-600 text-sm">Terminés</Text>
            </View>
            <View className="items-center">
              <Text className="text-2xl font-bold text-orange-600">
                {
                  filteredAppointments.filter(
                    (apt) => apt.status === "scheduled"
                  ).length
                }
              </Text>
              <Text className="text-gray-600 text-sm">En attente</Text>
            </View>
          </View>
          {selectedDate === today && (
            <View className="mt-3 pt-3 border-t border-gray-100">
              <Text className="text-gray-600 text-sm text-center">
                Rendez-vous triés par heure • Cliquez pour accéder au Post-RDV
              </Text>
            </View>
          )}
        </View>

        {/* Appointments List */}
        <View className="px-4 py-4">
          <Text className="text-lg font-semibold text-gray-900 mb-4">
            {selectedDate === today
              ? `Rendez-vous d'aujourd'hui (${filteredAppointments.length})`
              : `Rendez-vous du ${formatDate(selectedDate)} (${filteredAppointments.length})`}
          </Text>
          {isLoading ? (
            <View className="bg-white p-8 rounded-xl shadow-sm border border-gray-100 items-center">
              <ActivityIndicator size="large" color="#3b82f6" />
              <Text className="text-gray-500 text-lg font-medium mt-4">
                Chargement...
              </Text>
            </View>
          ) : filteredAppointments.length > 0 ? (
            filteredAppointments.map((appointment) => (
              <AppointmentCard key={appointment.id} appointment={appointment} />
            ))
          ) : (
            <View className="bg-white p-8 rounded-xl shadow-sm border border-gray-100 items-center">
              <IconSymbol
                name="calendar.badge.exclamationmark"
                size={48}
                color="#9ca3af"
              />
              <Text className="text-gray-500 text-lg font-medium mt-4">
                Aucun rendez-vous
              </Text>
              <Text className="text-gray-400 text-center mt-2">
                Aucun rendez-vous trouvé pour cette date
              </Text>
            </View>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
