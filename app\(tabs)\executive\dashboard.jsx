import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
} from 'react-native';
import { router } from 'expo-router';
import { IconSymbol } from '@/components/ui/IconSymbol';

// Mock data for Executive
const executiveData = {
  user: {
    name: '<PERSON>',
    role: 'Executive',
    avatar: '💼',
  },
  financialOverview: {
    totalRevenue: 125000,
    totalProfit: 32500,
    profitMargin: 26,
    totalBonusPaid: 8500,
    payrollCosts: 45000,
    roi: 72,
  },
  monthlyTrends: [
    { month: 'Jan', revenue: 98000, profit: 25480 },
    { month: 'Feb', revenue: 112000, profit: 29120 },
    { month: 'Mar', revenue: 125000, profit: 32500 },
  ],
  teamPerformance: [
    { role: 'Sales Assistants', count: 8, totalBonus: 3200, avgPerformance: 85 },
    { role: 'Representatives', count: 5, totalBonus: 4200, avgPerformance: 78 },
    { role: 'Recruiters', count: 2, totalBonus: 1100, avgPerformance: 92 },
  ],
  quickActions: [
    { id: 1, title: 'Payroll Management', icon: 'banknote', route: '/payrollPage' },
    { id: 2, title: 'Financial Reports', icon: 'chart.bar.doc.horizontal', route: '/financialReports' },
    { id: 3, title: 'Bonus Overview', icon: 'gift', route: '/bonusOverview' },
    { id: 4, title: 'Team Analytics', icon: 'person.3', route: '/teamAnalytics' },
  ],
  alerts: [
    { id: 1, type: 'warning', message: 'Monthly bonus budget at 85% capacity', priority: 'medium' },
    { id: 2, type: 'info', message: 'Q1 financial report ready for review', priority: 'low' },
    { id: 3, type: 'success', message: 'Revenue target exceeded by 12%', priority: 'high' },
  ],
};

export default function ExecutiveDashboardScreen() {
  const [selectedPeriod, setSelectedPeriod] = useState('monthly');

  const MetricCard = ({ title, value, subtitle, color = 'blue', icon, trend }) => (
    <View className="bg-white p-4 rounded-xl shadow-sm border border-gray-100 flex-1 mx-1 mb-3">
      <View className="flex-row items-center justify-between mb-2">
        <IconSymbol name={icon} size={20} color={`#${color === 'blue' ? '3b82f6' : color === 'green' ? '10b981' : color === 'purple' ? '8b5cf6' : color === 'orange' ? 'f59e0b' : 'dc2626'}`} />
        {trend && (
          <View className={`px-2 py-1 rounded-full ${trend > 0 ? 'bg-green-100' : 'bg-red-100'}`}>
            <Text className={`text-xs font-medium ${trend > 0 ? 'text-green-700' : 'text-red-700'}`}>
              {trend > 0 ? '+' : ''}{trend}%
            </Text>
          </View>
        )}
      </View>
      <Text className="text-gray-600 text-sm font-medium">{title}</Text>
      <Text className={`text-xl font-bold mt-1 text-${color}-600`}>{value}</Text>
      {subtitle && <Text className="text-gray-500 text-xs mt-1">{subtitle}</Text>}
    </View>
  );

  const QuickActionCard = ({ title, icon, onPress }) => (
    <TouchableOpacity
      className="bg-white p-4 rounded-xl shadow-sm border border-gray-100 items-center justify-center h-20 flex-1 mx-1"
      onPress={onPress}
    >
      <IconSymbol name={icon} size={20} color="#3b82f6" />
      <Text className="text-gray-700 text-xs font-medium mt-2 text-center">{title}</Text>
    </TouchableOpacity>
  );

  const TeamPerformanceCard = ({ role, count, totalBonus, avgPerformance }) => (
    <View className="bg-white p-4 rounded-xl shadow-sm border border-gray-100 mb-3">
      <View className="flex-row items-center justify-between mb-3">
        <Text className="text-gray-900 font-semibold">{role}</Text>
        <View className={`px-2 py-1 rounded-full ${
          avgPerformance >= 85 ? 'bg-green-100' : avgPerformance >= 70 ? 'bg-yellow-100' : 'bg-red-100'
        }`}>
          <Text className={`text-xs font-medium ${
            avgPerformance >= 85 ? 'text-green-700' : avgPerformance >= 70 ? 'text-yellow-700' : 'text-red-700'
          }`}>
            {avgPerformance}%
          </Text>
        </View>
      </View>
      <View className="flex-row justify-between">
        <View>
          <Text className="text-gray-500 text-sm">Team Size</Text>
          <Text className="text-gray-900 font-semibold">{count} members</Text>
        </View>
        <View>
          <Text className="text-gray-500 text-sm">Total Bonus</Text>
          <Text className="text-green-600 font-semibold">€{totalBonus.toLocaleString()}</Text>
        </View>
      </View>
    </View>
  );

  const AlertItem = ({ type, message, priority }) => (
    <View className={`p-3 rounded-lg mb-2 border-l-4 ${
      type === 'success' ? 'bg-green-50 border-green-500' :
      type === 'warning' ? 'bg-yellow-50 border-yellow-500' :
      'bg-blue-50 border-blue-500'
    }`}>
      <View className="flex-row items-center">
        <IconSymbol 
          name={type === 'success' ? 'checkmark.circle' : type === 'warning' ? 'exclamationmark.triangle' : 'info.circle'} 
          size={16} 
          color={type === 'success' ? '#10b981' : type === 'warning' ? '#f59e0b' : '#3b82f6'} 
        />
        <Text className="text-gray-900 text-sm font-medium ml-2 flex-1">{message}</Text>
        <View className={`px-2 py-1 rounded-full ${
          priority === 'high' ? 'bg-red-100' : priority === 'medium' ? 'bg-yellow-100' : 'bg-gray-100'
        }`}>
          <Text className={`text-xs font-medium ${
            priority === 'high' ? 'text-red-700' : priority === 'medium' ? 'text-yellow-700' : 'text-gray-700'
          }`}>
            {priority}
          </Text>
        </View>
      </View>
    </View>
  );

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <StatusBar barStyle="dark-content" backgroundColor="#f9fafb" />
      
      <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View className="bg-white px-4 py-6 border-b border-gray-100">
          <View className="flex-row items-center justify-between mb-6">
            <View>
              <Text className="text-2xl font-bold text-gray-900">
                Executive Dashboard
              </Text>
              <Text className="text-gray-600 mt-1">Financial Overview & Team Performance</Text>
            </View>
            <TouchableOpacity 
              className="w-12 h-12 bg-blue-100 rounded-full items-center justify-center"
              onPress={() => router.push('/profile')}
            >
              <Text className="text-xl">{executiveData.user.avatar}</Text>
            </TouchableOpacity>
          </View>

          {/* Period Selector */}
          <View className="flex-row space-x-2 mb-4">
            <TouchableOpacity 
              className={`px-4 py-2 rounded-lg ${selectedPeriod === 'monthly' ? 'bg-blue-500' : 'bg-gray-100'}`}
              onPress={() => setSelectedPeriod('monthly')}
            >
              <Text className={`font-medium ${selectedPeriod === 'monthly' ? 'text-white' : 'text-gray-700'}`}>
                Monthly
              </Text>
            </TouchableOpacity>
            <TouchableOpacity 
              className={`px-4 py-2 rounded-lg ${selectedPeriod === 'quarterly' ? 'bg-blue-500' : 'bg-gray-100'}`}
              onPress={() => setSelectedPeriod('quarterly')}
            >
              <Text className={`font-medium ${selectedPeriod === 'quarterly' ? 'text-white' : 'text-gray-700'}`}>
                Quarterly
              </Text>
            </TouchableOpacity>
          </View>

          {/* Financial Metrics */}
          <View className="flex-row mb-4">
            <MetricCard 
              title="Total Revenue" 
              value={`€${executiveData.financialOverview.totalRevenue.toLocaleString()}`}
              subtitle="This quarter"
              color="green"
              icon="chart.line.uptrend.xyaxis"
              trend={12}
            />
            <MetricCard 
              title="Total Profit" 
              value={`€${executiveData.financialOverview.totalProfit.toLocaleString()}`}
              subtitle={`${executiveData.financialOverview.profitMargin}% margin`}
              color="blue"
              icon="dollarsign.circle"
              trend={8}
            />
          </View>
          <View className="flex-row">
            <MetricCard 
              title="Bonus Paid" 
              value={`€${executiveData.financialOverview.totalBonusPaid.toLocaleString()}`}
              subtitle="Team bonuses"
              color="purple"
              icon="gift"
            />
            <MetricCard 
              title="ROI" 
              value={`${executiveData.financialOverview.roi}%`}
              subtitle="Return on investment"
              color="orange"
              icon="percent"
              trend={5}
            />
          </View>
        </View>

        {/* Quick Actions */}
        <View className="px-4 py-6">
          <Text className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</Text>
          <View className="flex-row flex-wrap">
            {executiveData.quickActions.map((action) => (
              <View key={action.id} className="w-1/2 p-1">
                <QuickActionCard
                  title={action.title}
                  icon={action.icon}
                  onPress={() => router.push(action.route)}
                />
              </View>
            ))}
          </View>
        </View>

        {/* Team Performance */}
        <View className="px-4 pb-6">
          <Text className="text-lg font-semibold text-gray-900 mb-4">Team Performance</Text>
          {executiveData.teamPerformance.map((team, index) => (
            <TeamPerformanceCard
              key={index}
              role={team.role}
              count={team.count}
              totalBonus={team.totalBonus}
              avgPerformance={team.avgPerformance}
            />
          ))}
        </View>

        {/* Alerts & Notifications */}
        <View className="px-4 pb-6">
          <Text className="text-lg font-semibold text-gray-900 mb-4">Alerts & Notifications</Text>
          {executiveData.alerts.map((alert) => (
            <AlertItem
              key={alert.id}
              type={alert.type}
              message={alert.message}
              priority={alert.priority}
            />
          ))}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
